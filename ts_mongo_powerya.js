stg_uri = "*******************************************************************************/";
prod_uri = "*******************************************************************************/";

uri = prod_uri;

db = connect(uri + "power_charge_realtime?authSource=admin");
db.createCollection("powerthanos_status", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "minutes",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("powerthanos_service", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});

db.powerthanos_status.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.powerthanos_service.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
stg_uri = "*******************************************************************************/";
prod_uri = "*******************************************************************************/";
eu_stg_uri = "mongodb://mongouser:CY80!Z3%5E6T5brD3a@10.99.233.9:27017/";
eu_prod_uri = "mongodb://mongouser:r5TsO9Hd#!eqv1hx@10.99.82.22:27017/";

uri = prod_uri;

db = connect(uri + "ts_di?authSource=admin");
db.createCollection("di_fypus1", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("di_pus3", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("di_pus4", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("di_pus2", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.di_fypus1.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.di_pus3.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.di_pus4.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.di_pus2.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);

db = connect(uri + "ts_sensor?authSource=admin");
db.createCollection("sensor_fypus1", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("sensor_pus3", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("sensor_pus4", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("sensor_pus2", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.sensor_fypus1.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.sensor_pus3.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.sensor_pus4.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.sensor_pus2.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);

db = connect(uri + "ts_converter?authSource=admin");
db.createCollection("converter_fypus1", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("converter_pus3", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("converter_pus4", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("converter_pus2", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.converter_fypus1.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.converter_pus3.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.converter_pus4.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.converter_pus2.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);

db = connect(uri + "ts_tank_transfer?authSource=admin");
db.createCollection("tank_transfer_pus3", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.tank_transfer_pus3.createIndex(
  { "metadata.device_id": 1, ts: -1 },
  { name: "deviceId_ts" }
);


db = connect(uri + "ts_oss_realtime?authSource=admin");
db.createCollection("realtime_fypus1", {
  timeseries: {
    timeField: "ts",
    metaField: "device_id",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("realtime_pus3", {
  timeseries: {
    timeField: "ts",
    metaField: "device_id",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("realtime_pus4", {
  timeseries: {
    timeField: "ts",
    metaField: "device_id",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("realtime_pus2", {
  timeseries: {
    timeField: "ts",
    metaField: "device_id",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.realtime_fypus1.createIndex(
  { "device_id": 1, ts: -1 },
  { name: "deviceId_ts" }
);
db.realtime_pus3.createIndex(
  { "device_id": 1, ts: -1 },
  { name: "deviceId_ts" }
);
db.realtime_pus4.createIndex(
  { "device_id": 1, ts: -1 },
  { name: "deviceId_ts" }
);
db.realtime_pus2.createIndex(
  { "device_id": 1, ts: -1 },
  { name: "deviceId_ts" }
);

db = connect(uri + "ts_welkin_realtime?authSource=admin");
db.createCollection("realtime_fypus1", {
  timeseries: {
    timeField: "ts",
    metaField: "device_id",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("realtime_pus3", {
  timeseries: {
    timeField: "ts",
    metaField: "device_id",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("realtime_pus4", {
  timeseries: {
    timeField: "ts",
    metaField: "device_id",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.realtime_fypus1.createIndex(
  { "device_id": 1, ts: -1 },
  { name: "deviceId_ts" }
);
db.realtime_pus3.createIndex(
  { "device_id": 1, ts: -1 },
  { name: "deviceId_ts" }
);
db.realtime_pus4.createIndex(
  { "device_id": 1, ts: -1 },
  { name: "deviceId_ts" }
);


db = connect(uri + "ts_plc_record?authSource=admin");
db.createCollection("plc_record_fypus1", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("plc_record_pus3", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("plc_record_pus4", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.createCollection("plc_record_pus2", {
  timeseries: {
    timeField: "ts",
    metaField: "metadata",
    granularity: "seconds",
  },
  expireAfterSeconds: 15552000,
});
db.plc_record_fypus1.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.plc_record_pus3.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.plc_record_pus4.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
db.plc_record_pus2.createIndex(
  { "metadata.device_id": 1, "ts": -1 },
  { name: "deviceId_ts" }
);
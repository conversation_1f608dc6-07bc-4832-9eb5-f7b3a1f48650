#!/bin/bash
cd /Users/<USER>/projects/welkin-website
export INST_NAME="ppd-welkin-website-tc-tke-stg"
export POD_IP="127.0.0.1"
export SERVICE_NAME="ppd-welkin-website"
export APOLLO_META="https://apollo-config-cn-stg.nioint.com"
export ENV="stg"
export APOLLO_ACCESSKEY_SECRET="NIO-ENCRYPT-START_6f1546b3f3bb8a4f5266ba60130d354ec3254d06641d3714902906a3fbd211e66ed8fa633548f0484a7335adcf66ca25_NIO-ENCRYPT-END"
go run . -config="config/config_local.json"

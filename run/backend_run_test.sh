#!/bin/bash
cd /Users/<USER>/projects/welkin-backend
export INST_NAME="ppd-welkin-backend-tc-tke-test"
export POD_IP="127.0.0.1"
export SERVICE_NAME="ppd-welkin-backend"
export APOLLO_META="https://apollo-config-cn-test.nioint.com"
export ENV="test"
export APOLLO_ACCESSKEY_SECRET="NIO-ENCRYPT-START_1299ab56ce4b6dd774192515222f6517ebf8225d9c8480fa23dede932b3e4d3b795a05fd119ee5593fc3745c8d60e00e_NIO-ENCRYPT-END"
go run . -logDir=/tmp/logs -config=config_test.json.json



#!/bin/bash
cd /Users/<USER>/projects/welkin-backend
export INST_NAME="ppd-welkin-backend-tc-tke-stg" 
export POD_IP="127.0.0.1" 
export SERVICE_NAME="ppd-welkin-backend" 
export APOLLO_META="https://apollo-config-cn-stg.nioint.com" ENV="stg" 
export APOLLO_ACCESSKEY_SECRET="NIO-ENCRYPT-START_70078af90a9896516b6364767b1a45c20fb96b870f9c958a17b782935828ec8d452c7435b1801d4a17a31a2f7734c88d_NIO-ENCRYPT-END" 
unset http_proxy
unset https_proxy
go run . -logDir=/tmp/logs -config=config_stg.json.json

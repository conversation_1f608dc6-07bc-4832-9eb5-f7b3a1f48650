{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "disabled": true, "alwaysAllow": []}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"GOOGLE_API_KEY": "AIzaSyDXc6KocviiehDKYpFEXRX5IyMoc_4yXwE"}, "disabled": true, "alwaysAllow": []}, "notionApi": {"command": "npx", "args": ["-y", "@notionhq/notion-mcp-server"], "env": {"OPENAPI_MCP_HEADERS": "{\"Authorization\": \"Bearer 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\", \"Notion-Version\": \"2022-06-28\" }"}, "alwaysAllow": ["API-post-search"], "disabled": true}, "browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"]}, "apple-mcp": {"command": "bunx", "args": ["--no-cache", "apple-mcp@latest"], "disabled": true, "alwaysAllow": []}, "figma-mcp": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"], "disabled": true, "alwaysAllow": []}, "excel": {"command": "uvx", "args": ["excel-mcp-server", "stdio"], "alwaysAllow": ["read_data_from_excel"], "disabled": false}, "server-chart-mcp": {"command": "npx", "args": ["-y", "@antv/mcp-server-chart"], "disabled": true, "alwaysAllow": []}, "mongodb": {"command": "npx", "args": ["-y", "mongodb-mcp-server", "--connectionString", "*****************************************************************************************************", "--readOnly"], "alwaysAllow": ["list-databases", "list-collections"], "disabled": true}, "git": {"command": "uvx", "args": ["mcp-server-git"], "disabled": true, "alwaysAllow": []}, "magic-ui": {"command": "npx", "args": ["-y", "@magicuidesign/mcp@latest"], "disabled": true, "alwaysAllow": ["getUIComponents", "getSpecialEffects", "getButtons", "getComponents", "getBackgrounds", "getTextAnimations"]}}}
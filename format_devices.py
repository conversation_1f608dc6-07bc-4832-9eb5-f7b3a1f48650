import json

def format_device_ids(input_file, output_file):
    """
    从JSON文件中读取设备ID列表，并将其逐行写入文本文件。
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            device_ids = json.load(f)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for device_id in device_ids:
                f.write(device_id + '\n')
        
        print(f"成功将 {len(device_ids)} 个设备ID写入到 {output_file}")

    except FileNotFoundError:
        print(f"错误: 输入文件 {input_file} 未找到。")
    except json.JSONDecodeError:
        print(f"错误: 无法解析输入文件 {input_file} 中的JSON。")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    format_device_ids('天宫_device_ids.json', 'psos_devices_pus4.txt')
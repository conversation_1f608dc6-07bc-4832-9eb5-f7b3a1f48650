ts_di.di_pus3
Long('15226933248')
Long('10263948940')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_di/collection-3--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 3394773,
      'blocks allocated': 25012276,
      'blocks freed': 22620821,
      'checkpoint size': Long('8898871296'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('5770702848'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('14669955072'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': Long('2324966302'),
      'bytes dirty in the cache cumulative': Long('2036355469137'),
      'bytes read into cache': Long('560687903416'),
      'bytes written from cache': Long('906731138295'),
      'checkpoint blocked page eviction': 8495,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 135176,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 15615407,
      'eviction walk target pages histogram - 0-9': 5877916,
      'eviction walk target pages histogram - 10-31': 9688062,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 21468,
      'eviction walk target pages histogram - 64-128': 27961,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 1862787,
      'eviction walks gave up because they restarted their walk twice': 3556873,
      'eviction walks gave up because they saw too many pages and found no candidates': 387991,
      'eviction walks gave up because they saw too many pages and found too few candidates': 102876,
      'eviction walks reached end of tree': 14477501,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 5914267,
      'eviction walks started from saved location in tree': 9701140,
      'hazard pointer blocked page eviction': 120595,
      'history store table insert calls': 19125050,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 3751,
      'in-memory page splits': 2240,
      'internal pages evicted': 620327,
      'internal pages split during eviction': 354,
      'leaf pages split during eviction': 1514155,
      'modified pages evicted': 12924758,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 9045420,
      'pages read into cache': 10576816,
      'pages read into cache after truncate': 8351,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 240708038,
      'pages seen by eviction walk': 575438988,
      'pages written from cache': 24865928,
      'pages written requiring in-memory restoration': 11853875,
      'the number of times full update inserted to history store': 17975454,
      'the number of times reverse modify inserted to history store': 1149596,
      'tracked dirty bytes in the cache': 5022158,
      'unmodified pages evicted': 12671457
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 194337,
      'pages removed': 1482188,
      'pages skipped during tree walk': 727276683,
      'pages visited': 743432348
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 8920969,
      'compressed pages written': 14146491,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 5614588,
      'number of blocks with compress ratio smaller than 2': 1314055,
      'number of blocks with compress ratio smaller than 32': 12490,
      'number of blocks with compress ratio smaller than 4': 512860,
      'number of blocks with compress ratio smaller than 64': 0,
      'number of blocks with compress ratio smaller than 8': 1466976,
      'page written failed to compress': 0,
      'page written was too small to compress': 10719437
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 65952,
      'Total number of entries skipped by cursor prev calls': 24994,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 25058097,
      'close calls that result in cache': 25058138,
      'create calls': 115431,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 3,
      'cursor next calls that skip less than 100 entries': 7209413,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 1,
      'cursor prev calls that skip less than 100 entries': 113159,
      'insert calls': 29973373,
      'insert key and value bytes': Long('1417852297554'),
      modify: 1149596,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('134278221779'),
      'next calls': 7209416,
      'open cursor count': 0,
      'operation restarted': 19,
      'prev calls': 113160,
      'remove calls': 4474316,
      'remove key bytes removed': 58166108,
      'reserve calls': 0,
      'reset calls': 102371230,
      'search calls': 23599575,
      'search history store calls': 0,
      'search near calls': 42865227,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': 394266336,
      'approximate byte size of transaction IDs in pages written': 197133168,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 46539844,
      'internal page multi-block writes': 243445,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 1531579,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 66,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 13345843,
      'page reconciliation calls for eviction': 12044181,
      'pages deleted': 35954,
      'pages written including an aggregated newest start durable timestamp ': 8062259,
      'pages written including an aggregated newest stop durable timestamp ': 83353,
      'pages written including an aggregated newest stop timestamp ': 56838,
      'pages written including an aggregated newest stop transaction ID': 56838,
      'pages written including an aggregated newest transaction ID ': 8105209,
      'pages written including an aggregated oldest start timestamp ': 4366766,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 13388671,
      'pages written including at least one start timestamp': 13388671,
      'pages written including at least one start transaction ID': 13388671,
      'pages written including at least one stop durable timestamp': 1490605,
      'pages written including at least one stop timestamp': 1490605,
      'pages written including at least one stop transaction ID': 1490605,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 20166934,
      'records written including a start timestamp': 20166934,
      'records written including a start transaction ID': 20166934,
      'records written including a stop durable timestamp': 4474712,
      'records written including a stop timestamp': 4474712,
      'records written including a stop transaction ID': 4474712
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 10263960379,
  numOrphanDocs: 0,
  storageSize: 14669955072,
  totalIndexSize: 556978176,
  totalSize: 15226933248,
  timeseries: {
    bucketCount: 6373974,
    numBucketInserts: 10848290,
    numBucketUpdates: 8278672,
    numBucketsOpenedDueToMetadata: 1108,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 10574010,
    numBucketsClosedDueToTimeForward: 273066,
    numBucketsClosedDueToTimeBackward: 106,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 19126962,
    numWaits: 1,
    numMeasurementsCommitted: 4144471655,
    avgNumMeasurementsPerCommit: 216,
    numBytesUncompressed: 1072728691167,
    numBytesCompressed: 17401426424,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 10846411,
    numUncompressedBuckets: 0,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 1610,
    bucketsNs: 'ts_di.system.buckets.di_pus3'
  },
  indexSizes: { deviceId_ts: 556978176 },
  avgObjSize: 0,
  ns: 'ts_di.di_pus3',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_di.di_pus4
Long('21500973056')
Long('7392230082')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_di/collection-4--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 4592676,
      'blocks allocated': 36777547,
      'blocks freed': 33285843,
      'checkpoint size': Long('13405835264'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('7838380032'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('21244694528'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 11026112,
      'bytes dirty in the cache cumulative': Long('4233782526444'),
      'bytes read into cache': Long('360529309349'),
      'bytes written from cache': Long('2031514148558'),
      'checkpoint blocked page eviction': 12166,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 155889,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 17124109,
      'eviction walk target pages histogram - 0-9': 5033407,
      'eviction walk target pages histogram - 10-31': 12029588,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 35205,
      'eviction walk target pages histogram - 64-128': 25909,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 2243076,
      'eviction walks gave up because they restarted their walk twice': 2115876,
      'eviction walks gave up because they saw too many pages and found no candidates': 494564,
      'eviction walks gave up because they saw too many pages and found too few candidates': 217619,
      'eviction walks reached end of tree': 11643244,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 5074976,
      'eviction walks started from saved location in tree': 12049133,
      'hazard pointer blocked page eviction': 136579,
      'history store table insert calls': 60920936,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 60687,
      'in-memory page splits': 31457,
      'internal pages evicted': 514203,
      'internal pages split during eviction': 529,
      'leaf pages split during eviction': 2663413,
      'modified pages evicted': 23158657,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 20807851,
      'pages read into cache': 6792954,
      'pages read into cache after truncate': 7633,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 558852053,
      'pages seen by eviction walk': 650725933,
      'pages written from cache': 36631223,
      'pages written requiring in-memory restoration': 21789212,
      'the number of times full update inserted to history store': 60491341,
      'the number of times reverse modify inserted to history store': 429595,
      'tracked dirty bytes in the cache': 9400688,
      'unmodified pages evicted': 10733345
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 233621,
      'pages removed': 2096313,
      'pages skipped during tree walk': 798807856,
      'pages visited': 810542009
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 4229090,
      'compressed pages written': 21916549,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 3494848,
      'number of blocks with compress ratio smaller than 2': 213929,
      'number of blocks with compress ratio smaller than 32': 850,
      'number of blocks with compress ratio smaller than 4': 71103,
      'number of blocks with compress ratio smaller than 64': 0,
      'number of blocks with compress ratio smaller than 8': 448360,
      'page written failed to compress': 0,
      'page written was too small to compress': 14714674
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 21121,
      'Total number of entries skipped by cursor prev calls': 9008,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 116223852,
      'close calls that result in cache': 116223844,
      'create calls': 142680,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 10,
      'cursor next calls that skip less than 100 entries': 2785190,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 5,
      'cursor prev calls that skip less than 100 entries': 113041,
      'insert calls': 66765461,
      'insert key and value bytes': Long('4155252370525'),
      modify: 429595,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('5020247345'),
      'next calls': 2785200,
      'open cursor count': 32,
      'operation restarted': 17,
      'prev calls': 113046,
      'remove calls': 2338132,
      'remove key bytes removed': 30395716,
      'reserve calls': 0,
      'reset calls': 309659295,
      'search calls': 63283033,
      'search history store calls': 0,
      'search near calls': 124330963,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': 573581888,
      'approximate byte size of transaction IDs in pages written': 286790944,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 50369307,
      'internal page multi-block writes': 202926,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 2805513,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 17,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 23858131,
      'page reconciliation calls for eviction': 21976260,
      'pages deleted': 29215,
      'pages written including an aggregated newest start durable timestamp ': 7474095,
      'pages written including an aggregated newest stop durable timestamp ': 135645,
      'pages written including an aggregated newest stop timestamp ': 49368,
      'pages written including an aggregated newest stop transaction ID': 49368,
      'pages written including an aggregated newest transaction ID ': 7509710,
      'pages written including an aggregated oldest start timestamp ': 6635432,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 23937046,
      'pages written including at least one start timestamp': 23937046,
      'pages written including at least one start transaction ID': 23937046,
      'pages written including at least one stop durable timestamp': 2098650,
      'pages written including at least one stop timestamp': 2098650,
      'pages written including at least one stop transaction ID': 2098650,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 33510509,
      'records written including a start timestamp': 33510509,
      'records written including a start transaction ID': 33510509,
      'records written including a stop durable timestamp': 2338359,
      'records written including a stop timestamp': 2338359,
      'records written including a stop transaction ID': 2338359
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 7392230082,
  numOrphanDocs: 0,
  storageSize: 21244694528,
  totalIndexSize: 256278528,
  totalSize: 21500973056,
  timeseries: {
    bucketCount: 3506318,
    numBucketInserts: 5844450,
    numBucketUpdates: 55078081,
    numBucketsOpenedDueToMetadata: 886,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 5604859,
    numBucketsClosedDueToTimeForward: 218039,
    numBucketsClosedDueToTimeBackward: 20666,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 60922531,
    numWaits: 1244,
    numMeasurementsCommitted: 2755995894,
    avgNumMeasurementsPerCommit: 45,
    numBytesUncompressed: 715748864459,
    numBytesCompressed: 12212085245,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 5842931,
    numUncompressedBuckets: 0,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 2108,
    bucketsNs: 'ts_di.system.buckets.di_pus4'
  },
  indexSizes: { deviceId_ts: 256278528 },
  avgObjSize: 0,
  ns: 'ts_di.di_pus4',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_sensor.sensor_pus3
Long('13784047616')
Long('130843745810')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_sensor/collection-13--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 3230442,
      'blocks allocated': 22059292,
      'blocks freed': 20347537,
      'checkpoint size': Long('5671485440'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('7916183552'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('13587869696'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 6724992,
      'bytes dirty in the cache cumulative': Long('1877356983370'),
      'bytes read into cache': Long('856597185450'),
      'bytes written from cache': Long('1308224151264'),
      'checkpoint blocked page eviction': 6107,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 122990,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 18187540,
      'eviction walk target pages histogram - 0-9': 9734447,
      'eviction walk target pages histogram - 10-31': 8269677,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 29895,
      'eviction walk target pages histogram - 64-128': 153521,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 1463367,
      'eviction walks gave up because they restarted their walk twice': 7832261,
      'eviction walks gave up because they saw too many pages and found no candidates': 394553,
      'eviction walks gave up because they saw too many pages and found too few candidates': 51108,
      'eviction walks reached end of tree': 23306469,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 9750147,
      'eviction walks started from saved location in tree': 8437393,
      'hazard pointer blocked page eviction': 102289,
      'history store table insert calls': 9762278,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 8,
      'in-memory page splits': 4,
      'internal pages evicted': 629107,
      'internal pages split during eviction': 296,
      'leaf pages split during eviction': 3191490,
      'modified pages evicted': 10708124,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 6041313,
      'pages read into cache': 9846741,
      'pages read into cache after truncate': 4097,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 110313108,
      'pages seen by eviction walk': 514525358,
      'pages written from cache': 21841116,
      'pages written requiring in-memory restoration': 9124953,
      'the number of times full update inserted to history store': 9404210,
      'the number of times reverse modify inserted to history store': 358068,
      'tracked dirty bytes in the cache': 4853581,
      'unmodified pages evicted': 11536933
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 90517,
      'pages removed': 2038222,
      'pages skipped during tree walk': 796011623,
      'pages visited': 800356308
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 9247226,
      'compressed pages written': 13702078,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 2747570,
      'number of blocks with compress ratio smaller than 2': 11573,
      'number of blocks with compress ratio smaller than 32': 6049246,
      'number of blocks with compress ratio smaller than 4': 50885,
      'number of blocks with compress ratio smaller than 64': 208767,
      'number of blocks with compress ratio smaller than 8': 179185,
      'page written failed to compress': 0,
      'page written was too small to compress': 8139038
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 70107,
      'Total number of entries skipped by cursor prev calls': 27955,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 16142727,
      'close calls that result in cache': 16142768,
      'create calls': 116455,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 6,
      'cursor next calls that skip less than 100 entries': 2535397,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 2,
      'cursor prev calls that skip less than 100 entries': 113016,
      'insert calls': 13394873,
      'insert key and value bytes': Long('1088853885243'),
      modify: 358068,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('11747255417'),
      'next calls': 2535522,
      'open cursor count': 1,
      'operation restarted': 6,
      'prev calls': 113018,
      'remove calls': 2175279,
      'remove key bytes removed': 28278627,
      'reserve calls': 0,
      'reset calls': 53580477,
      'search calls': 11938978,
      'search history store calls': 0,
      'search near calls': 21866902,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': 193510352,
      'approximate byte size of transaction IDs in pages written': 96755176,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 45437155,
      'internal page multi-block writes': 289835,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 3211973,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 6,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 11189844,
      'page reconciliation calls for eviction': 9977432,
      'pages deleted': 25985,
      'pages written including an aggregated newest start durable timestamp ': 7778110,
      'pages written including an aggregated newest stop durable timestamp ': 80583,
      'pages written including an aggregated newest stop timestamp ': 52825,
      'pages written including an aggregated newest stop transaction ID': 52825,
      'pages written including an aggregated newest transaction ID ': 7818272,
      'pages written including an aggregated oldest start timestamp ': 5045355,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 8423031,
      'pages written including at least one start timestamp': 8423031,
      'pages written including at least one start transaction ID': 8423031,
      'pages written including at least one stop durable timestamp': 2038886,
      'pages written including at least one stop timestamp': 2038886,
      'pages written including at least one stop transaction ID': 2038886,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 9919059,
      'records written including a start timestamp': 9919059,
      'records written including a start transaction ID': 9919059,
      'records written including a stop durable timestamp': 2175338,
      'records written including a stop timestamp': 2175338,
      'records written including a stop transaction ID': 2175338
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 130843745810,
  numOrphanDocs: 0,
  storageSize: 13587869696,
  totalIndexSize: 196177920,
  totalSize: 13784047616,
  timeseries: {
    bucketCount: 1457295,
    numBucketInserts: 3632574,
    numBucketUpdates: 6133132,
    numBucketsOpenedDueToMetadata: 1109,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 3204058,
    numBucketsClosedDueToTimeForward: 427376,
    numBucketsClosedDueToTimeBackward: 31,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 9765706,
    numWaits: 2,
    numMeasurementsCommitted: 620525673,
    avgNumMeasurementsPerCommit: 63,
    numBytesUncompressed: 412569916369,
    numBytesCompressed: 324384927623,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 3629167,
    numUncompressedBuckets: 10,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 89785,
    bucketsNs: 'ts_sensor.system.buckets.sensor_pus3'
  },
  indexSizes: { deviceId_ts: 196177920 },
  avgObjSize: 0,
  ns: 'ts_sensor.sensor_pus3',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_sensor.sensor_pus4
Long('48267436032')
Long('49439735258')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_sensor/collection-14--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 10345395,
      'blocks allocated': 62173499,
      'blocks freed': 56411015,
      'checkpoint size': Long('22266359808'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('25291542528'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('47558348800'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 14208056,
      'bytes dirty in the cache cumulative': Long('5958755336828'),
      'bytes read into cache': Long('557962177020'),
      'bytes written from cache': Long('2995900720440'),
      'checkpoint blocked page eviction': 12024,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 211001,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 24421148,
      'eviction walk target pages histogram - 0-9': 7274169,
      'eviction walk target pages histogram - 10-31': 16811914,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 60629,
      'eviction walk target pages histogram - 64-128': 274436,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 3111854,
      'eviction walks gave up because they restarted their walk twice': 3341577,
      'eviction walks gave up because they saw too many pages and found no candidates': 739453,
      'eviction walks gave up because they saw too many pages and found too few candidates': 308568,
      'eviction walks reached end of tree': 17245737,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 7508094,
      'eviction walks started from saved location in tree': 16913054,
      'hazard pointer blocked page eviction': 172024,
      'history store table insert calls': 90998162,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 183315,
      'in-memory page splits': 93043,
      'internal pages evicted': 979836,
      'internal pages split during eviction': 1226,
      'leaf pages split during eviction': 4390211,
      'modified pages evicted': 35817353,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 27851099,
      'pages read into cache': 13503510,
      'pages read into cache after truncate': 9964,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 915221533,
      'pages seen by eviction walk': 1116206145,
      'pages written from cache': 61955381,
      'pages written requiring in-memory restoration': 33446767,
      'the number of times full update inserted to history store': 89287621,
      'the number of times reverse modify inserted to history store': 1710541,
      'tracked dirty bytes in the cache': 13062920,
      'unmodified pages evicted': 23280285
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 741166,
      'pages removed': 6819825,
      'pages skipped during tree walk': 1236633686,
      'pages visited': 1253886387
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 12131893,
      'compressed pages written': 47076991,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 3345988,
      'number of blocks with compress ratio smaller than 2': 2087957,
      'number of blocks with compress ratio smaller than 32': 1740276,
      'number of blocks with compress ratio smaller than 4': 3800475,
      'number of blocks with compress ratio smaller than 64': 0,
      'number of blocks with compress ratio smaller than 8': 1157198,
      'page written failed to compress': 0,
      'page written was too small to compress': 14878390
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 75539,
      'Total number of entries skipped by cursor prev calls': 35983,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 170903218,
      'close calls that result in cache': 170903177,
      'create calls': 168972,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 26,
      'cursor next calls that skip less than 100 entries': 8859612,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 13,
      'cursor prev calls that skip less than 100 entries': 112982,
      'insert calls': 105672809,
      'insert key and value bytes': Long('6711799127059'),
      modify: 1710541,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('34281549309'),
      'next calls': 8859638,
      'open cursor count': 49,
      'operation restarted': 114,
      'prev calls': 112995,
      'remove calls': 8455220,
      'remove key bytes removed': 109917860,
      'reserve calls': 0,
      'reset calls': 475708094,
      'search calls': 99455792,
      'search history store calls': 0,
      'search near calls': 190676283,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': 979969168,
      'approximate byte size of transaction IDs in pages written': 489984584,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 79287829,
      'internal page multi-block writes': 331369,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 4633415,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 17,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 36860162,
      'page reconciliation calls for eviction': 33753655,
      'pages deleted': 86641,
      'pages written including an aggregated newest start durable timestamp ': 12893754,
      'pages written including an aggregated newest stop durable timestamp ': 143965,
      'pages written including an aggregated newest stop timestamp ': 114966,
      'pages written including an aggregated newest stop transaction ID': 114966,
      'pages written including an aggregated newest transaction ID ': 12994149,
      'pages written including an aggregated oldest start timestamp ': 10784517,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 36731642,
      'pages written including at least one start timestamp': 36731642,
      'pages written including at least one start transaction ID': 36731642,
      'pages written including at least one stop durable timestamp': 6824056,
      'pages written including at least one stop timestamp': 6824056,
      'pages written including at least one stop transaction ID': 6824056,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 52792593,
      'records written including a start timestamp': 52792593,
      'records written including a start transaction ID': 52792593,
      'records written including a stop durable timestamp': 8455480,
      'records written including a stop timestamp': 8455480,
      'records written including a stop transaction ID': 8455480
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 49439735258,
  numOrphanDocs: 0,
  storageSize: 47558348800,
  totalIndexSize: 709087232,
  totalSize: 48267436032,
  timeseries: {
    bucketCount: 6219325,
    numBucketInserts: 14674545,
    numBucketUpdates: 76325685,
    numBucketsOpenedDueToMetadata: 889,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 14316886,
    numBucketsClosedDueToTimeForward: 305194,
    numBucketsClosedDueToTimeBackward: 51576,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 91000230,
    numWaits: 2619,
    numMeasurementsCommitted: 3902455046,
    avgNumMeasurementsPerCommit: 42,
    numBytesUncompressed: 1803478318508,
    numBytesCompressed: 117155237152,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 14672583,
    numUncompressedBuckets: 0,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 7949,
    bucketsNs: 'ts_sensor.system.buckets.sensor_pus4'
  },
  indexSizes: { deviceId_ts: 709087232 },
  avgObjSize: 0,
  ns: 'ts_sensor.sensor_pus4',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_sensor.sensor_pus2
Long('6952529920')
Long('38341229834')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_sensor/collection-15--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 1569926,
      'blocks allocated': 12598368,
      'blocks freed': 11700620,
      'checkpoint size': Long('2445225984'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('4387209216'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('6832590848'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 2419640,
      'bytes dirty in the cache cumulative': Long('884406914492'),
      'bytes read into cache': Long('415658090847'),
      'bytes written from cache': Long('576909079061'),
      'checkpoint blocked page eviction': 1456,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 71252,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 13739789,
      'eviction walk target pages histogram - 0-9': 9085052,
      'eviction walk target pages histogram - 10-31': 4582447,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 12799,
      'eviction walk target pages histogram - 64-128': 59491,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 741776,
      'eviction walks gave up because they restarted their walk twice': 8154494,
      'eviction walks gave up because they saw too many pages and found no candidates': 288192,
      'eviction walks gave up because they saw too many pages and found too few candidates': 15544,
      'eviction walks reached end of tree': 21052051,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 9221786,
      'eviction walks started from saved location in tree': 4518003,
      'hazard pointer blocked page eviction': 49508,
      'history store table insert calls': 4311112,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 0,
      'in-memory page splits': 0,
      'internal pages evicted': 719034,
      'internal pages split during eviction': 22,
      'leaf pages split during eviction': 1468938,
      'modified pages evicted': 5846394,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 2929084,
      'pages read into cache': 5622677,
      'pages read into cache after truncate': 2678,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 46875463,
      'pages seen by eviction walk': 269423029,
      'pages written from cache': 12386740,
      'pages written requiring in-memory restoration': 4499851,
      'the number of times full update inserted to history store': 4290161,
      'the number of times reverse modify inserted to history store': 20951,
      'tracked dirty bytes in the cache': 1417725,
      'unmodified pages evicted': 5722857
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 65919,
      'pages removed': 936314,
      'pages skipped during tree walk': 525697757,
      'pages visited': 528424834
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 4905899,
      'compressed pages written': 6782351,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 3846144,
      'number of blocks with compress ratio smaller than 2': 7452,
      'number of blocks with compress ratio smaller than 32': 689405,
      'number of blocks with compress ratio smaller than 4': 31116,
      'number of blocks with compress ratio smaller than 64': 1152,
      'number of blocks with compress ratio smaller than 8': 330630,
      'page written failed to compress': 0,
      'page written was too small to compress': 5604389
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 12002,
      'Total number of entries skipped by cursor prev calls': 3736,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 7803661,
      'close calls that result in cache': 7803703,
      'create calls': 105080,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 0,
      'cursor next calls that skip less than 100 entries': 1412408,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 0,
      'cursor prev calls that skip less than 100 entries': 113034,
      'insert calls': 6176025,
      'insert key and value bytes': Long('416146884732'),
      modify: 20951,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': 528456450,
      'next calls': 1412408,
      'open cursor count': 0,
      'operation restarted': 0,
      'prev calls': 113034,
      'remove calls': 1155324,
      'remove key bytes removed': 15019212,
      'reserve calls': 0,
      'reset calls': 25046390,
      'search calls': 5467380,
      'search history store calls': 0,
      'search near calls': 9910905,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': 95242592,
      'approximate byte size of transaction IDs in pages written': 47621296,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 29949376,
      'internal page multi-block writes': 192602,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 1470684,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 2,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 6239598,
      'page reconciliation calls for eviction': 5346724,
      'pages deleted': 13497,
      'pages written including an aggregated newest start durable timestamp ': 5326053,
      'pages written including an aggregated newest stop durable timestamp ': 146279,
      'pages written including an aggregated newest stop timestamp ': 36850,
      'pages written including an aggregated newest stop transaction ID': 36850,
      'pages written including an aggregated newest transaction ID ': 5352711,
      'pages written including an aggregated oldest start timestamp ': 1827996,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 4353209,
      'pages written including at least one start timestamp': 4353209,
      'pages written including at least one start transaction ID': 4353209,
      'pages written including at least one stop durable timestamp': 938483,
      'pages written including at least one stop timestamp': 938483,
      'pages written including at least one stop transaction ID': 938483,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 4797235,
      'records written including a start timestamp': 4797235,
      'records written including a start transaction ID': 4797235,
      'records written including a stop durable timestamp': 1155427,
      'records written including a stop timestamp': 1155427,
      'records written including a stop transaction ID': 1155427
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 38341229834,
  numOrphanDocs: 0,
  storageSize: 6832590848,
  totalIndexSize: 119939072,
  totalSize: 6952529920,
  timeseries: {
    bucketCount: 709583,
    numBucketInserts: 1864907,
    numBucketUpdates: 2448348,
    numBucketsOpenedDueToMetadata: 1120,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 1361417,
    numBucketsClosedDueToTimeForward: 478024,
    numBucketsClosedDueToTimeBackward: 24346,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 4313255,
    numWaits: 0,
    numMeasurementsCommitted: 519846516,
    avgNumMeasurementsPerCommit: 120,
    numBytesUncompressed: 182708187926,
    numBytesCompressed: 101072462945,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 1862770,
    numUncompressedBuckets: 3,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 54033,
    bucketsNs: 'ts_sensor.system.buckets.sensor_pus2'
  },
  indexSizes: { deviceId_ts: 119939072 },
  avgObjSize: 0,
  ns: 'ts_sensor.sensor_pus2',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_converter.converter_pus3
Long('19269632000')
Long('12352264629')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_converter/collection-23--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 4170089,
      'blocks allocated': 29878293,
      'blocks freed': 27810210,
      'checkpoint size': Long('7311167488'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('11422179328'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('18733740032'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 5099148,
      'bytes dirty in the cache cumulative': Long('2362774799284'),
      'bytes read into cache': Long('710336845737'),
      'bytes written from cache': Long('1096731499254'),
      'checkpoint blocked page eviction': 20415,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 173224,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 21144783,
      'eviction walk target pages histogram - 0-9': 9407764,
      'eviction walk target pages histogram - 10-31': 11646677,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 16699,
      'eviction walk target pages histogram - 64-128': 73643,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 2158235,
      'eviction walks gave up because they restarted their walk twice': 6671692,
      'eviction walks gave up because they saw too many pages and found no candidates': 458854,
      'eviction walks gave up because they saw too many pages and found too few candidates': 95659,
      'eviction walks reached end of tree': 23325177,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 9391372,
      'eviction walks started from saved location in tree': 11753411,
      'hazard pointer blocked page eviction': 140673,
      'history store table insert calls': 21947404,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 1049,
      'in-memory page splits': 623,
      'internal pages evicted': 664603,
      'internal pages split during eviction': 415,
      'leaf pages split during eviction': 2660265,
      'modified pages evicted': 14290075,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 10501967,
      'pages read into cache': 11148230,
      'pages read into cache after truncate': 5264,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 274239699,
      'pages seen by eviction walk': 686381983,
      'pages written from cache': 29660111,
      'pages written requiring in-memory restoration': 12951302,
      'the number of times full update inserted to history store': 21425394,
      'the number of times reverse modify inserted to history store': 522010,
      'tracked dirty bytes in the cache': 3878108,
      'unmodified pages evicted': 13979873
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 1277549,
      'pages removed': 2553026,
      'pages skipped during tree walk': 1009444977,
      'pages visited': 1020824917
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 9696908,
      'compressed pages written': 16233386,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 5230149,
      'number of blocks with compress ratio smaller than 2': 942019,
      'number of blocks with compress ratio smaller than 32': 1,
      'number of blocks with compress ratio smaller than 4': 1536632,
      'number of blocks with compress ratio smaller than 64': 0,
      'number of blocks with compress ratio smaller than 8': 1988107,
      'page written failed to compress': 0,
      'page written was too small to compress': 13426725
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 654243,
      'Total number of entries skipped by cursor prev calls': 314479,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 29537382,
      'close calls that result in cache': 29537420,
      'create calls': 130338,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 14,
      'cursor next calls that skip less than 100 entries': 7064206,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 7,
      'cursor prev calls that skip less than 100 entries': 112934,
      'insert calls': 33160701,
      'insert key and value bytes': Long('1637650141726'),
      modify: 522010,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('6127366426'),
      'next calls': 7064220,
      'open cursor count': 1,
      'operation restarted': 19,
      'prev calls': 112941,
      'remove calls': 6726418,
      'remove key bytes removed': 87443434,
      'reserve calls': 0,
      'reset calls': 120194614,
      'search calls': 28673927,
      'search history store calls': 0,
      'search near calls': 50769967,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': 480167696,
      'approximate byte size of transaction IDs in pages written': 240083848,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 49613476,
      'internal page multi-block writes': 323394,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 2719897,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 37,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 14911562,
      'page reconciliation calls for eviction': 13195147,
      'pages deleted': 62460,
      'pages written including an aggregated newest start durable timestamp ': 10838123,
      'pages written including an aggregated newest stop durable timestamp ': 103162,
      'pages written including an aggregated newest stop timestamp ': 75832,
      'pages written including an aggregated newest stop transaction ID': 75832,
      'pages written including an aggregated newest transaction ID ': 10901160,
      'pages written including an aggregated oldest start timestamp ': 6028267,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 13041647,
      'pages written including at least one start timestamp': 13041647,
      'pages written including at least one start transaction ID': 13041647,
      'pages written including at least one stop durable timestamp': 2577971,
      'pages written including at least one stop timestamp': 2577971,
      'pages written including at least one stop transaction ID': 2577971,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 23283554,
      'records written including a start timestamp': 23283554,
      'records written including a start transaction ID': 23283554,
      'records written including a stop durable timestamp': 6726927,
      'records written including a stop timestamp': 6726927,
      'records written including a stop transaction ID': 6726927
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 12352264629,
  numOrphanDocs: 0,
  storageSize: 18733740032,
  totalIndexSize: 535891968,
  totalSize: 19269632000,
  timeseries: {
    bucketCount: 4486858,
    numBucketInserts: 11213276,
    numBucketUpdates: 10735944,
    numBucketsOpenedDueToMetadata: 1109,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 10800604,
    numBucketsClosedDueToTimeForward: 404855,
    numBucketsClosedDueToTimeBackward: 6708,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 21949220,
    numWaits: 16,
    numMeasurementsCommitted: 5490598210,
    avgNumMeasurementsPerCommit: 250,
    numBytesUncompressed: 1089578504335,
    numBytesCompressed: 30817251763,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 11211481,
    numUncompressedBuckets: 0,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 2752,
    bucketsNs: 'ts_converter.system.buckets.converter_pus3'
  },
  indexSizes: { deviceId_ts: 535891968 },
  avgObjSize: 0,
  ns: 'ts_converter.converter_pus3',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_converter.converter_pus4
Long('25045463040')
Long('6553686840')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_converter/collection-24--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 5198292,
      'blocks allocated': 49593497,
      'blocks freed': 46852409,
      'checkpoint size': Long('9890070528'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('14883368960'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('24773931008'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 10644681,
      'bytes dirty in the cache cumulative': Long('5740550923128'),
      'bytes read into cache': Long('511817730388'),
      'bytes written from cache': Long('2782448954018'),
      'checkpoint blocked page eviction': 73521,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 282393,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 24238726,
      'eviction walk target pages histogram - 0-9': 7415853,
      'eviction walk target pages histogram - 10-31': 16768410,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 14209,
      'eviction walk target pages histogram - 64-128': 40254,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 3148136,
      'eviction walks gave up because they restarted their walk twice': 3319816,
      'eviction walks gave up because they saw too many pages and found no candidates': 707035,
      'eviction walks gave up because they saw too many pages and found too few candidates': 321919,
      'eviction walks reached end of tree': 17490379,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 7502512,
      'eviction walks started from saved location in tree': 16736214,
      'hazard pointer blocked page eviction': 198504,
      'history store table insert calls': 83066247,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 47674,
      'in-memory page splits': 25040,
      'internal pages evicted': 571387,
      'internal pages split during eviction': 554,
      'leaf pages split during eviction': 3233893,
      'modified pages evicted': 31902465,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 28734365,
      'pages read into cache': 9702817,
      'pages read into cache after truncate': 6437,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 769500164,
      'pages seen by eviction walk': 857247879,
      'pages written from cache': 49375381,
      'pages written requiring in-memory restoration': 29772454,
      'the number of times full update inserted to history store': 79600377,
      'the number of times reverse modify inserted to history store': 3465870,
      'tracked dirty bytes in the cache': 9507404,
      'unmodified pages evicted': 13022405
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 4928331,
      'pages removed': 3107286,
      'pages skipped during tree walk': 1100298423,
      'pages visited': 1124827919
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 6151288,
      'compressed pages written': 30012772,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 4272520,
      'number of blocks with compress ratio smaller than 2': 286324,
      'number of blocks with compress ratio smaller than 32': 0,
      'number of blocks with compress ratio smaller than 4': 203405,
      'number of blocks with compress ratio smaller than 64': 0,
      'number of blocks with compress ratio smaller than 8': 1389039,
      'page written failed to compress': 0,
      'page written was too small to compress': 19362609
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 692980,
      'Total number of entries skipped by cursor prev calls': 348043,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 160999324,
      'close calls that result in cache': 160999267,
      'create calls': 161522,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 22,
      'cursor next calls that skip less than 100 entries': 3727863,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 11,
      'cursor prev calls that skip less than 100 entries': 112920,
      'insert calls': 89151094,
      'insert key and value bytes': Long('5529042196366'),
      modify: 3465870,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('26712707185'),
      'next calls': 3727885,
      'open cursor count': 78,
      'operation restarted': 12,
      'prev calls': 112931,
      'remove calls': 3506397,
      'remove key bytes removed': 45583161,
      'reserve calls': 0,
      'reset calls': 423450807,
      'search calls': 86590111,
      'search history store calls': 0,
      'search near calls': 169793463,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': 760243328,
      'approximate byte size of transaction IDs in pages written': 380121664,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 70883016,
      'internal page multi-block writes': 300599,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 3432955,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 29,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 33131968,
      'page reconciliation calls for eviction': 29964548,
      'pages deleted': 161992,
      'pages written including an aggregated newest start durable timestamp ': 10841270,
      'pages written including an aggregated newest stop durable timestamp ': 99616,
      'pages written including an aggregated newest stop timestamp ': 72792,
      'pages written including an aggregated newest stop transaction ID': 72792,
      'pages written including an aggregated newest transaction ID ': 10901022,
      'pages written including an aggregated oldest start timestamp ': 7765171,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 31628906,
      'pages written including at least one start timestamp': 31628906,
      'pages written including at least one start transaction ID': 31628906,
      'pages written including at least one stop durable timestamp': 3171615,
      'pages written including at least one stop timestamp': 3171615,
      'pages written including at least one stop transaction ID': 3171615,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 44008606,
      'records written including a start timestamp': 44008606,
      'records written including a start transaction ID': 44008606,
      'records written including a stop durable timestamp': 3506602,
      'records written including a stop timestamp': 3506602,
      'records written including a stop transaction ID': 3506602
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 6553696290,
  numOrphanDocs: 0,
  storageSize: 24773931008,
  totalIndexSize: 271532032,
  totalSize: 25045463040,
  timeseries: {
    bucketCount: 2578361,
    numBucketInserts: 6084758,
    numBucketUpdates: 76982721,
    numBucketsOpenedDueToMetadata: 889,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 5756729,
    numBucketsClosedDueToTimeForward: 306862,
    numBucketsClosedDueToTimeBackward: 20278,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 83067479,
    numWaits: 3206,
    numMeasurementsCommitted: 3868790317,
    avgNumMeasurementsPerCommit: 46,
    numBytesUncompressed: 729683953024,
    numBytesCompressed: 15409647326,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 6083616,
    numUncompressedBuckets: 0,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 2541,
    bucketsNs: 'ts_converter.system.buckets.converter_pus4'
  },
  indexSizes: { deviceId_ts: 271532032 },
  avgObjSize: 0,
  ns: 'ts_converter.converter_pus4',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_converter.converter_pus2
Long('45887578112')
Long('18506298293')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_converter/collection-25--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 8911657,
      'blocks allocated': 93477697,
      'blocks freed': 88729982,
      'checkpoint size': Long('18110263296'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('27140702208'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('45252194304'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 23714000,
      'bytes dirty in the cache cumulative': Long('11852350607188'),
      'bytes read into cache': Long('1014486861297'),
      'bytes written from cache': Long('5713841774354'),
      'checkpoint blocked page eviction': 138355,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 312592,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 27388344,
      'eviction walk target pages histogram - 0-9': 6693961,
      'eviction walk target pages histogram - 10-31': 19765151,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 763060,
      'eviction walk target pages histogram - 64-128': 166172,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 3657587,
      'eviction walks gave up because they restarted their walk twice': 1869378,
      'eviction walks gave up because they saw too many pages and found no candidates': 877065,
      'eviction walks gave up because they saw too many pages and found too few candidates': 487726,
      'eviction walks reached end of tree': 13947041,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 6894936,
      'eviction walks started from saved location in tree': 20493408,
      'hazard pointer blocked page eviction': 201517,
      'history store table insert calls': 155978902,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 79597,
      'in-memory page splits': 41105,
      'internal pages evicted': 1328919,
      'internal pages split during eviction': 1141,
      'leaf pages split during eviction': 6763052,
      'modified pages evicted': 67313944,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 59710913,
      'pages read into cache': 20099178,
      'pages read into cache after truncate': 9389,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 1488449551,
      'pages seen by eviction walk': 1488293187,
      'pages written from cache': 93259503,
      'pages written requiring in-memory restoration': 62385932,
      'the number of times full update inserted to history store': 149067673,
      'the number of times reverse modify inserted to history store': 6911229,
      'tracked dirty bytes in the cache': 21373670,
      'unmodified pages evicted': 26243201
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 2167605,
      'pages removed': 6895315,
      'pages skipped during tree walk': 1378618164,
      'pages visited': 1410646908
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 15678196,
      'compressed pages written': 71190572,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 9050312,
      'number of blocks with compress ratio smaller than 2': 3994363,
      'number of blocks with compress ratio smaller than 32': 0,
      'number of blocks with compress ratio smaller than 4': 560464,
      'number of blocks with compress ratio smaller than 64': 0,
      'number of blocks with compress ratio smaller than 8': 2073059,
      'page written failed to compress': 0,
      'page written was too small to compress': 22068931
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 1186274,
      'Total number of entries skipped by cursor prev calls': 393846,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 300941036,
      'close calls that result in cache': 300940775,
      'create calls': 172344,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 74,
      'cursor next calls that skip less than 100 entries': 8147699,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 9,
      'cursor prev calls that skip less than 100 entries': 112955,
      'insert calls': 168509349,
      'insert key and value bytes': Long('10398588477298'),
      modify: 6911229,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('66463193617'),
      'next calls': 8147773,
      'open cursor count': 218,
      'operation restarted': 36,
      'prev calls': 112964,
      'remove calls': 7737407,
      'remove key bytes removed': 100586291,
      'reserve calls': 0,
      'reset calls': 797104334,
      'search calls': 163755165,
      'search history store calls': 0,
      'search near calls': 319916070,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': 1585509152,
      'approximate byte size of transaction IDs in pages written': 792754576,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 113302190,
      'internal page multi-block writes': 325550,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 7076784,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 23,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 69238785,
      'page reconciliation calls for eviction': 62718255,
      'pages deleted': 119971,
      'pages written including an aggregated newest start durable timestamp ': 13443858,
      'pages written including an aggregated newest stop durable timestamp ': 237170,
      'pages written including an aggregated newest stop timestamp ': 115623,
      'pages written including an aggregated newest stop transaction ID': 115623,
      'pages written including an aggregated newest transaction ID ': 13545231,
      'pages written including an aggregated oldest start timestamp ': 7856125,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 65221495,
      'pages written including at least one start timestamp': 65221495,
      'pages written including at least one start transaction ID': 65221495,
      'pages written including at least one stop durable timestamp': 6931074,
      'pages written including at least one stop timestamp': 6931074,
      'pages written including at least one stop transaction ID': 6931074,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 91356769,
      'records written including a start timestamp': 91356769,
      'records written including a start transaction ID': 91356769,
      'records written including a stop durable timestamp': 7737553,
      'records written including a stop timestamp': 7737553,
      'records written including a stop transaction ID': 7737553
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 18506308893,
  numOrphanDocs: 0,
  storageSize: 45252194304,
  totalIndexSize: 635383808,
  totalSize: 45887578112,
  timeseries: {
    bucketCount: 4792874,
    numBucketInserts: 12530281,
    numBucketUpdates: 143450641,
    numBucketsOpenedDueToMetadata: 1120,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 11826983,
    numBucketsClosedDueToTimeForward: 617626,
    numBucketsClosedDueToTimeBackward: 84552,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 155980922,
    numWaits: 5584,
    numMeasurementsCommitted: 7224065650,
    avgNumMeasurementsPerCommit: 46,
    numBytesUncompressed: 1510216356573,
    numBytesCompressed: 48400250108,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 12528433,
    numUncompressedBuckets: 0,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 3861,
    bucketsNs: 'ts_converter.system.buckets.converter_pus2'
  },
  indexSizes: { deviceId_ts: 635383808 },
  avgObjSize: 0,
  ns: 'ts_converter.converter_pus2',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_plc_record.plc_record_pus3
Long('286333067264')
Long('791645239123')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_plc_record/collection-55--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 36901526,
      'blocks allocated': 114889147,
      'blocks freed': 100017994,
      'checkpoint size': Long('124809166848'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('153930956800'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('278742208512'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 6,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 20222808,
      'bytes dirty in the cache cumulative': Long('6422547901498'),
      'bytes read into cache': Long('1877947833504'),
      'bytes written from cache': Long('5997364207758'),
      'checkpoint blocked page eviction': 202313,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 414597,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 29348097,
      'eviction walk target pages histogram - 0-9': 6506821,
      'eviction walk target pages histogram - 10-31': 18363143,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 2217329,
      'eviction walk target pages histogram - 64-128': 2260804,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 3374419,
      'eviction walks gave up because they restarted their walk twice': 3092666,
      'eviction walks gave up because they saw too many pages and found no candidates': 930042,
      'eviction walks gave up because they saw too many pages and found too few candidates': 376866,
      'eviction walks reached end of tree': 17096944,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 7777681,
      'eviction walks started from saved location in tree': 21570416,
      'hazard pointer blocked page eviction': 139323,
      'history store table insert calls': 185399717,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 198210,
      'in-memory page splits': 100670,
      'internal pages evicted': 2986759,
      'internal pages split during eviction': 3671,
      'leaf pages split during eviction': 13448266,
      'modified pages evicted': 57628070,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 3,
      'page written requiring history store records': 34337039,
      'pages read into cache': 33717820,
      'pages read into cache after truncate': 8397,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': Long('3243133818'),
      'pages seen by eviction walk': Long('2948829865'),
      'pages written from cache': 114670955,
      'pages written requiring in-memory restoration': 45324910,
      'the number of times full update inserted to history store': 184668317,
      'the number of times reverse modify inserted to history store': 731400,
      'tracked dirty bytes in the cache': 15515513,
      'unmodified pages evicted': 56852246
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 1313891,
      'pages removed': 21765623,
      'pages skipped during tree walk': 1813948179,
      'pages visited': 1864984618
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 31131262,
      'compressed pages written': 94184295,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 9123596,
      'number of blocks with compress ratio smaller than 2': 70,
      'number of blocks with compress ratio smaller than 32': 67,
      'number of blocks with compress ratio smaller than 4': 98356,
      'number of blocks with compress ratio smaller than 64': 0,
      'number of blocks with compress ratio smaller than 8': 21909174,
      'page written failed to compress': 0,
      'page written was too small to compress': 20486659
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 4796845,
      'Total number of entries skipped by cursor prev calls': 2266909,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 33351592,
      'close calls that result in cache': 33351629,
      'create calls': 133620,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 1749,
      'cursor next calls that skip less than 100 entries': 109176135,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 360,
      'cursor prev calls that skip less than 100 entries': 396967,
      'insert calls': 360121198,
      'insert key and value bytes': Long('10056460677114'),
      modify: 731400,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('88263105479'),
      'next calls': 109177884,
      'open cursor count': 8,
      'operation restarted': 2024,
      'prev calls': 397327,
      'remove calls': 104334494,
      'remove key bytes removed': 1356348422,
      'reserve calls': 0,
      'reset calls': 973726169,
      'search calls': 290140837,
      'search history store calls': 0,
      'search near calls': 475912255,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': Long('4810716576'),
      'approximate byte size of transaction IDs in pages written': Long('2405358288'),
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 211067980,
      'internal page multi-block writes': 745500,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 13662474,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 60,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 59735773,
      'page reconciliation calls for eviction': 52120058,
      'pages deleted': 460891,
      'pages written including an aggregated newest start durable timestamp ': 19184375,
      'pages written including an aggregated newest stop durable timestamp ': 542861,
      'pages written including an aggregated newest stop timestamp ': 525211,
      'pages written including an aggregated newest stop transaction ID': 525211,
      'pages written including an aggregated newest transaction ID ': 19689600,
      'pages written including an aggregated oldest start timestamp ': 13349629,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 62200494,
      'pages written including at least one start timestamp': 62200494,
      'pages written including at least one start transaction ID': 62200494,
      'pages written including at least one stop durable timestamp': 21782106,
      'pages written including at least one stop timestamp': 21782106,
      'pages written including at least one stop transaction ID': 21782105,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 196334624,
      'records written including a start timestamp': 196334624,
      'records written including a start transaction ID': 196334624,
      'records written including a stop durable timestamp': 104335162,
      'records written including a stop timestamp': 104335162,
      'records written including a stop transaction ID': 104335162
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 791645239123,
  numOrphanDocs: 0,
  storageSize: 278742208512,
  totalIndexSize: 7590858752,
  totalSize: 286333067264,
  timeseries: {
    bucketCount: 70386810,
    numBucketInserts: 174721304,
    numBucketUpdates: 10688352,
    numBucketsOpenedDueToMetadata: 1109,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 174321973,
    numBucketsClosedDueToTimeForward: 398155,
    numBucketsClosedDueToTimeBackward: 67,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 185409656,
    numWaits: 6,
    numMeasurementsCommitted: 5459010695,
    avgNumMeasurementsPerCommit: 29,
    numBytesUncompressed: 7812700237589,
    numBytesCompressed: 1965149663332,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 174711546,
    numUncompressedBuckets: 5851,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 11247,
    bucketsNs: 'ts_plc_record.system.buckets.plc_record_pus3'
  },
  indexSizes: { deviceId_ts: 7590858752 },
  avgObjSize: 0,
  ns: 'ts_plc_record.plc_record_pus3',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_plc_record.plc_record_pus4
Long('136264941568')
Long('274954716235')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_plc_record/collection-56--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 22010882,
      'blocks allocated': 89716273,
      'blocks freed': 78304392,
      'checkpoint size': Long('64036089856'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('69621129216'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('133658394624'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 5,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 16153386,
      'bytes dirty in the cache cumulative': Long('6083384268918'),
      'bytes read into cache': Long('899689734783'),
      'bytes written from cache': Long('3849731893582'),
      'checkpoint blocked page eviction': 155046,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 290956,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 25929016,
      'eviction walk target pages histogram - 0-9': 6885939,
      'eviction walk target pages histogram - 10-31': 18092972,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 169901,
      'eviction walk target pages histogram - 64-128': 780204,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 3081668,
      'eviction walks gave up because they restarted their walk twice': 3457224,
      'eviction walks gave up because they saw too many pages and found no candidates': 801911,
      'eviction walks gave up because they saw too many pages and found too few candidates': 341935,
      'eviction walks reached end of tree': 17084420,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 7694198,
      'eviction walks started from saved location in tree': 18234818,
      'hazard pointer blocked page eviction': 144693,
      'history store table insert calls': 126896264,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 576576,
      'in-memory page splits': 280805,
      'internal pages evicted': 1773933,
      'internal pages split during eviction': 2486,
      'leaf pages split during eviction': 4204607,
      'modified pages evicted': 46225481,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 2,
      'page written requiring history store records': 29927137,
      'pages read into cache': 22005588,
      'pages read into cache after truncate': 11815,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': 1576764246,
      'pages seen by eviction walk': 1820803405,
      'pages written from cache': 89498133,
      'pages written requiring in-memory restoration': 34158197,
      'the number of times full update inserted to history store': 125159081,
      'the number of times reverse modify inserted to history store': 1737183,
      'tracked dirty bytes in the cache': 15063381,
      'unmodified pages evicted': 34130749
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 2087698,
      'pages removed': 13460067,
      'pages skipped during tree walk': Long('2407120787'),
      'pages visited': Long('2460629639')
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 20493321,
      'compressed pages written': 70921667,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 4093878,
      'number of blocks with compress ratio smaller than 2': 18229,
      'number of blocks with compress ratio smaller than 32': 125307,
      'number of blocks with compress ratio smaller than 4': 6349167,
      'number of blocks with compress ratio smaller than 64': 0,
      'number of blocks with compress ratio smaller than 8': 9906741,
      'page written failed to compress': 0,
      'page written was too small to compress': 18576466
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 5699997,
      'Total number of entries skipped by cursor prev calls': 2804433,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 197370341,
      'close calls that result in cache': 197370249,
      'create calls': 178283,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 440,
      'cursor next calls that skip less than 100 entries': 33989434,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 105,
      'cursor prev calls that skip less than 100 entries': 113052,
      'insert calls': 183481556,
      'insert key and value bytes': Long('8435107144624'),
      modify: 1737183,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': Long('110011697700'),
      'next calls': 33989874,
      'open cursor count': 105,
      'operation restarted': 1844,
      'prev calls': 113157,
      'remove calls': 32594887,
      'remove key bytes removed': 423733531,
      'reserve calls': 0,
      'reset calls': 700376226,
      'search calls': 159636756,
      'search history store calls': 0,
      'search near calls': 286926260,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': Long('2151192960'),
      'approximate byte size of transaction IDs in pages written': 1075596480,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 104743491,
      'internal page multi-block writes': 594986,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 4190038,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 62,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 47390986,
      'page reconciliation calls for eviction': 41535404,
      'pages deleted': 330255,
      'pages written including an aggregated newest start durable timestamp ': 17603131,
      'pages written including an aggregated newest stop durable timestamp ': 370965,
      'pages written including an aggregated newest stop timestamp ': 351946,
      'pages written including an aggregated newest stop transaction ID': 351946,
      'pages written including an aggregated newest transaction ID ': 17937062,
      'pages written including an aggregated oldest start timestamp ': 13147263,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 52625118,
      'pages written including at least one start timestamp': 52625118,
      'pages written including at least one start transaction ID': 52625118,
      'pages written including at least one stop durable timestamp': 13505734,
      'pages written including at least one stop timestamp': 13505734,
      'pages written including at least one stop transaction ID': 13505733,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 101854378,
      'records written including a start timestamp': 101854378,
      'records written including a start transaction ID': 101854378,
      'records written including a stop durable timestamp': 32595182,
      'records written including a stop timestamp': 32595182,
      'records written including a stop transaction ID': 32595182
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 0
    }
  },
  sharded: false,
  size: 274954716235,
  numOrphanDocs: 0,
  storageSize: 133658394624,
  totalIndexSize: 2606546944,
  totalSize: 136264941568,
  timeseries: {
    bucketCount: 23990282,
    numBucketInserts: 56585169,
    numBucketUpdates: 70312306,
    numBucketsOpenedDueToMetadata: 889,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 56211477,
    numBucketsClosedDueToTimeForward: 257578,
    numBucketsClosedDueToTimeBackward: 115225,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 126897475,
    numWaits: 2304,
    numMeasurementsCommitted: 3868899931,
    avgNumMeasurementsPerCommit: 30,
    numBytesUncompressed: 4862594413967,
    numBytesCompressed: 648535794253,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 56584087,
    numUncompressedBuckets: 83,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 11461,
    bucketsNs: 'ts_plc_record.system.buckets.plc_record_pus4'
  },
  indexSizes: { deviceId_ts: 2606546944 },
  avgObjSize: 0,
  ns: 'ts_plc_record.plc_record_pus4',
  nindexes: 1,
  scaleFactor: 1
}
########################
ts_plc_record.plc_record_pus2
Long('205539938304')
Long('257310525189')
{
  ok: 1,
  capped: false,
  wiredTiger: {
    metadata: { formatVersion: 1 },
    creationString: 'access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=zstd,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=u,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=false),lsm=(auto_throttle=true,bloom=true,bloom_bit_count=16,bloom_config=,bloom_hash_count=8,bloom_oldest=false,chunk_count_limit=0,chunk_max=5GB,chunk_size=10MB,merge_custom=(prefix=,start_generation=0,suffix=),merge_max=15,merge_min=0),memory_page_image_max=0,memory_page_max=10m,os_cache_dirty_max=0,os_cache_max=0,prefix_compression=false,prefix_compression_min=4,readonly=false,source=,split_deepen_min_child=0,split_deepen_per_child=0,split_pct=90,tiered_object=false,tiered_storage=(auth_token=,bucket=,bucket_prefix=,cache_directory=,local_retention=300,name=,object_target_size=0),type=file,value_format=u,verbose=[],write_timestamp_usage=none',
    type: 'file',
    uri: 'statistics:table:ts_plc_record/collection-57--2195476461141011986',
    LSM: {
      'bloom filter false positives': 0,
      'bloom filter hits': 0,
      'bloom filter misses': 0,
      'bloom filter pages evicted from cache': 0,
      'bloom filter pages read into cache': 0,
      'bloom filters in the LSM tree': 0,
      'chunks in the LSM tree': 0,
      'highest merge generation in the LSM tree': 0,
      'queries that could have benefited from a Bloom filter that did not exist': 0,
      'sleep for LSM checkpoint throttle': 0,
      'sleep for LSM merge throttle': 0,
      'total size of bloom filters': 0
    },
    'block-manager': {
      'allocations requiring file extension': 29035662,
      'blocks allocated': 155796150,
      'blocks freed': 137375099,
      'checkpoint size': Long('87492816896'),
      'file allocation unit size': 4096,
      'file bytes available for reuse': Long('114833469440'),
      'file magic number': 120897,
      'file major version number': 1,
      'file size in bytes': Long('202328911872'),
      'minor version number': 0
    },
    btree: {
      'btree checkpoint generation': 110807,
      'btree clean tree checkpoint expiration time': 0,
      'btree compact pages reviewed': 0,
      'btree compact pages rewritten': 0,
      'btree compact pages skipped': 0,
      'btree skipped by compaction as process would not reduce size': 0,
      'column-store fixed-size leaf pages': 0,
      'column-store fixed-size time windows': 0,
      'column-store internal pages': 0,
      'column-store variable-size RLE encoded values': 0,
      'column-store variable-size deleted values': 0,
      'column-store variable-size leaf pages': 0,
      'fixed-record size': 0,
      'maximum internal page size': 4096,
      'maximum leaf page key size': 2867,
      'maximum leaf page size': 32768,
      'maximum leaf page value size': 67108864,
      'maximum tree depth': 6,
      'number of key/value pairs': 0,
      'overflow pages': 0,
      'row-store empty values': 0,
      'row-store internal pages': 0,
      'row-store leaf pages': 0
    },
    cache: {
      'bytes currently in the cache': 227228767,
      'bytes dirty in the cache cumulative': Long('10404786583107'),
      'bytes read into cache': Long('1241469895541'),
      'bytes written from cache': Long('5861883083092'),
      'checkpoint blocked page eviction': 247910,
      'checkpoint of history store file blocked non-history store page eviction': 0,
      'data source pages selected for eviction unable to be evicted': 452155,
      'eviction gave up due to detecting an out of order on disk value behind the last update on the chain': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update': 0,
      'eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain': 0,
      'eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update': 0,
      'eviction gave up due to needing to remove a record from the history store but checkpoint is running': 0,
      'eviction walk passes of a file': 29111282,
      'eviction walk target pages histogram - 0-9': 6074749,
      'eviction walk target pages histogram - 10-31': 20229133,
      'eviction walk target pages histogram - 128 and higher': 0,
      'eviction walk target pages histogram - 32-63': 1305060,
      'eviction walk target pages histogram - 64-128': 1502340,
      'eviction walk target pages reduced due to history store cache pressure': 0,
      'eviction walks abandoned': 3523305,
      'eviction walks gave up because they restarted their walk twice': 1928298,
      'eviction walks gave up because they saw too many pages and found no candidates': 905516,
      'eviction walks gave up because they saw too many pages and found too few candidates': 825898,
      'eviction walks reached end of tree': 13626301,
      'eviction walks restarted': 0,
      'eviction walks started from root of tree': 7191266,
      'eviction walks started from saved location in tree': 21920016,
      'hazard pointer blocked page eviction': 139011,
      'history store table insert calls': 216901896,
      'history store table insert calls that returned restart': 0,
      'history store table out-of-order resolved updates that lose their durable timestamp': 0,
      'history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp': 0,
      'history store table reads': 0,
      'history store table reads missed': 0,
      'history store table reads requiring squashed modifies': 0,
      'history store table truncation by rollback to stable to remove an unstable update': 0,
      'history store table truncation by rollback to stable to remove an update': 0,
      'history store table truncation to remove an update': 0,
      'history store table truncation to remove range of updates due to key being removed from the data page during reconciliation': 0,
      'history store table truncation to remove range of updates due to out-of-order timestamp update on data page': 0,
      'history store table writes requiring squashed modifies': 0,
      'in-memory page passed criteria to be split': 606923,
      'in-memory page splits': 297148,
      'internal pages evicted': 4549677,
      'internal pages split during eviction': 4552,
      'leaf pages split during eviction': 6959598,
      'modified pages evicted': 89215345,
      'overflow pages read into cache': 0,
      'page split during eviction deepened the tree': 3,
      'page written requiring history store records': 57342286,
      'pages read into cache': 43403996,
      'pages read into cache after truncate': 7510,
      'pages read into cache after truncate in prepare state': 0,
      'pages requested from the cache': Long('2990099760'),
      'pages seen by eviction walk': Long('4652526645'),
      'pages written from cache': 155577956,
      'pages written requiring in-memory restoration': 70973201,
      'the number of times full update inserted to history store': 216884625,
      'the number of times reverse modify inserted to history store': 17271,
      'tracked dirty bytes in the cache': 25417109,
      'unmodified pages evicted': 69130451
    },
    cache_walk: {
      'Average difference between current eviction generation when the page was last considered': 0,
      'Average on-disk page image size seen': 0,
      'Average time in cache for pages that have been visited by the eviction server': 0,
      'Average time in cache for pages that have not been visited by the eviction server': 0,
      'Clean pages currently in cache': 0,
      'Current eviction generation': 0,
      'Dirty pages currently in cache': 0,
      'Entries in the root page': 0,
      'Internal pages currently in cache': 0,
      'Leaf pages currently in cache': 0,
      'Maximum difference between current eviction generation when the page was last considered': 0,
      'Maximum page size seen': 0,
      'Minimum on-disk page image size seen': 0,
      'Number of pages never visited by eviction server': 0,
      'On-disk page image sizes smaller than a single allocation unit': 0,
      'Pages created in memory and never written': 0,
      'Pages currently queued for eviction': 0,
      'Pages that could not be queued for eviction': 0,
      'Refs skipped during cache traversal': 0,
      'Size of the root page': 0,
      'Total number of pages currently in cache': 0
    },
    'checkpoint-cleanup': {
      'pages added for eviction': 4320881,
      'pages removed': 26597348,
      'pages skipped during tree walk': Long('2296256528'),
      'pages visited': Long('2445953236')
    },
    compression: {
      'compressed page maximum internal page size prior to compression': 4096,
      'compressed page maximum leaf page size prior to compression ': 131072,
      'compressed pages read': 39416085,
      'compressed pages written': 132790453,
      'number of blocks with compress ratio greater than 64': 0,
      'number of blocks with compress ratio smaller than 16': 2153353,
      'number of blocks with compress ratio smaller than 2': 22301,
      'number of blocks with compress ratio smaller than 32': 104057,
      'number of blocks with compress ratio smaller than 4': 20801086,
      'number of blocks with compress ratio smaller than 64': 16,
      'number of blocks with compress ratio smaller than 8': 16335277,
      'page written failed to compress': 0,
      'page written was too small to compress': 22787502
    },
    cursor: {
      'Total number of entries skipped by cursor next calls': 9086070,
      'Total number of entries skipped by cursor prev calls': 4042783,
      'Total number of entries skipped to position the history store cursor': 0,
      'Total number of times a search near has exited due to prefix config': 0,
      'bulk loaded cursor insert calls': 0,
      'cache cursors reuse count': 360441298,
      'close calls that result in cache': 360440854,
      'create calls': 186571,
      'cursor next calls that skip due to a globally visible history store tombstone': 0,
      'cursor next calls that skip greater than or equal to 100 entries': 1766,
      'cursor next calls that skip less than 100 entries': 46899139,
      'cursor prev calls that skip due to a globally visible history store tombstone': 0,
      'cursor prev calls that skip greater than or equal to 100 entries': 180,
      'cursor prev calls that skip less than 100 entries': 113009,
      'insert calls': 290087828,
      'insert key and value bytes': Long('13079952385516'),
      modify: 17271,
      'modify key and value bytes affected': 0,
      'modify value bytes modified': 1631053379,
      'next calls': 46900980,
      'open cursor count': 430,
      'operation restarted': 15145,
      'prev calls': 113189,
      'remove calls': 45193073,
      'remove key bytes removed': 587509949,
      'reserve calls': 0,
      'reset calls': 1175694060,
      'search calls': 262225009,
      'search history store calls': 0,
      'search near calls': 479969070,
      'truncate calls': 0,
      'update calls': 0,
      'update key and value bytes': 0,
      'update value size change': 0
    },
    reconciliation: {
      'approximate byte size of timestamps in pages written': Long('3401034576'),
      'approximate byte size of transaction IDs in pages written': 1700517288,
      'dictionary matches': 0,
      'fast-path pages deleted': 0,
      'internal page key bytes discarded using suffix compression': 151346735,
      'internal page multi-block writes': 1092519,
      'leaf page key bytes discarded using prefix compression': 0,
      'leaf page multi-block writes': 6913946,
      'leaf-page overflow keys': 0,
      'maximum blocks required for a page': 98,
      'overflow values written': 0,
      'page checksum matches': 0,
      'page reconciliation calls': 91335421,
      'page reconciliation calls for eviction': 78698682,
      'pages deleted': 635881,
      'pages written including an aggregated newest start durable timestamp ': 20965954,
      'pages written including an aggregated newest stop durable timestamp ': 812197,
      'pages written including an aggregated newest stop timestamp ': 697926,
      'pages written including an aggregated newest stop transaction ID': 697926,
      'pages written including an aggregated newest transaction ID ': 21642611,
      'pages written including an aggregated oldest start timestamp ': 13814148,
      'pages written including an aggregated prepare': 0,
      'pages written including at least one prepare': 0,
      'pages written including at least one start durable timestamp': 98747559,
      'pages written including at least one start timestamp': 98747559,
      'pages written including at least one start transaction ID': 98747559,
      'pages written including at least one stop durable timestamp': 26658224,
      'pages written including at least one stop timestamp': 26658224,
      'pages written including at least one stop transaction ID': 26658224,
      'records written including a prepare': 0,
      'records written including a start durable timestamp': 167370108,
      'records written including a start timestamp': 167370108,
      'records written including a start transaction ID': 167370108,
      'records written including a stop durable timestamp': 45194553,
      'records written including a stop timestamp': 45194553,
      'records written including a stop transaction ID': 45194553
    },
    session: {
      'object compaction': 0,
      'tiered operations dequeued and processed': 0,
      'tiered operations scheduled': 0,
      'tiered storage local retention time (secs)': 0
    },
    transaction: {
      'race to read prepared update retry': 0,
      'rollback to stable history store records with stop timestamps older than newer records': 0,
      'rollback to stable inconsistent checkpoint': 0,
      'rollback to stable keys removed': 0,
      'rollback to stable keys restored': 0,
      'rollback to stable restored tombstones from history store': 0,
      'rollback to stable restored updates from history store': 0,
      'rollback to stable skipping delete rle': 0,
      'rollback to stable skipping stable rle': 0,
      'rollback to stable sweeping history store keys': 0,
      'rollback to stable updates removed from history store': 0,
      'transaction checkpoints due to obsolete pages': 0,
      'update conflicts': 1
    }
  },
  sharded: false,
  size: 257310525189,
  numOrphanDocs: 0,
  storageSize: 202328911872,
  totalIndexSize: 3211026432,
  totalSize: 205539938304,
  timeseries: {
    bucketCount: 27992626,
    numBucketInserts: 73185699,
    numBucketUpdates: 143718001,
    numBucketsOpenedDueToMetadata: 1311,
    numBucketsClosedDueToCount: 0,
    numBucketsClosedDueToSchemaChange: 0,
    numBucketsClosedDueToSize: 72473277,
    numBucketsClosedDueToTimeForward: 601148,
    numBucketsClosedDueToTimeBackward: 110004,
    numBucketsClosedDueToMemoryThreshold: 0,
    numCommits: 216903700,
    numWaits: 2113,
    numMeasurementsCommitted: 7227827155,
    avgNumMeasurementsPerCommit: 33,
    numBytesUncompressed: 6103673512146,
    numBytesCompressed: 672182343324,
    numSubObjCompressionRestart: 0,
    numCompressedBuckets: 73184165,
    numUncompressedBuckets: 1,
    numFailedDecompressBuckets: 0,
    avgBucketSize: 9192,
    bucketsNs: 'ts_plc_record.system.buckets.plc_record_pus2'
  },
  indexSizes: { deviceId_ts: 3211026432 },
  avgObjSize: 0,
  ns: 'ts_plc_record.plc_record_pus2',
  nindexes: 1,
  scaleFactor: 1
}
########################


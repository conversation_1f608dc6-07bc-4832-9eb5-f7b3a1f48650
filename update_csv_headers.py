import io
import json
import csv
import sys
import os

def process_csv_header(json_path, csv_path):
    """
    Updates the header of a CSV file based on a JSON mapping,
    ensuring UTF-8 encoding (with BOM) for Python 2 and 3 compatibility.
    Saves the output to a new file with a '_new' suffix.
    """
    # 1. Read the JSON file using io.open for encoding support in Python 2.
    try:
        with io.open(json_path, 'r', encoding='utf-8') as f:
            data_map_list = json.load(f)
    except IOError:
        print("Error: JSON file not found at {}".format(json_path))
        return
    except ValueError:
        print("Error: Could not decode JSON from {}".format(json_path))
        return

    data_id_to_name = {item['data_id']: item['var_cn_name'] for item in data_map_list if 'data_id' in item and 'var_cn_name' in item}

    # Create a new filename
    base, ext = os.path.splitext(csv_path)
    new_csv_path = base + "_new" + ext

    try:
        with io.open(csv_path, 'r', encoding='utf-8', errors='ignore') as f_in, \
             io.open(new_csv_path, 'w', encoding='utf-8-sig', newline='') as f_out:

            reader = csv.reader(f_in)
            writer = csv.writer(f_out)

            try:
                original_header = next(reader)
            except StopIteration:
                print("Warning: CSV file {} is empty.".format(csv_path))
                return

            new_header = []
            for header in original_header:
                if header.startswith('r') and header[1:].isdigit():
                    data_id = header[1:]
                    new_header.append(data_id_to_name.get(data_id, header))
                else:
                    new_header.append(header)

            writer.writerow(new_header)
            for row in reader:
                writer.writerow(row)
        
        print("Successfully created new file with updated header: {}".format(new_csv_path))

    except IOError:
        print("Error: CSV file not found at {}".format(csv_path))
        return
    except Exception as e:
        print("An error occurred while processing {}: {}".format(csv_path, e))
        return


if __name__ == "__main__":
    # --- Debug Information ---
    print("--- Debug Information ---")
    try:
        cwd = os.getcwd()
        print("Current Working Directory: {}".format(cwd))
        print("Contents of CWD: {}".format(os.listdir('.')))
        target_dir = 'realtime/realtime_oss'
        print("Checking existence of '{}': {}".format(target_dir, os.path.exists(target_dir)))
        if os.path.exists(target_dir):
            print("Contents of '{}': {}".format(target_dir, os.listdir(target_dir)))
    except Exception as e:
        print("Error during debugging: {}".format(e))
    print("--- End Debug Information ---\n")
    # --- End Debug Information ---

    
    json_file_path = "realtime/realtime_welkin/device2cloud.welkin-realtime-pus3.json"
    csv_file_path = sys.argv[1]
    
    process_csv_header(json_file_path, csv_file_path)
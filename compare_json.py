import json

def compare_json_files(file1_path, file2_path):
    """
    比较两个 JSON 文件的内容是否完全一致。

    Args:
        file1_path (str): 第一个 JSON 文件的路径。
        file2_path (str): 第二个 JSON 文件的路径。

    Returns:
        bool: 如果两个文件的内容完全一致，则返回 True；否则返回 False。
    """
    try:
        with open(file1_path, 'r') as f1, open(file2_path, 'r') as f2:
            data1 = json.load(f1)
            data2 = json.load(f2)

        return data1 == data2
    except FileNotFoundError:
        return False  # 如果文件不存在，则返回 False
    except json.JSONDecodeError:
        return False  # 如果文件不是有效的 JSON 格式，则返回 False


if __name__ == "__main__":
    file1_path = "/Users/<USER>/Downloads/response.json"
    file2_path = "/Users/<USER>/Downloads/response1.json"

    if compare_json_files(file1_path, file2_path):
        print(f"文件 '{file1_path}' 和 '{file2_path}' 的内容完全一致。")
    else:
        print(f"文件 '{file1_path}' 和 '{file2_path}' 的内容不一致。")
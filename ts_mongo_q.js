stg_uri = "*******************************************************************************/";
prod_uri = "*******************************************************************************/";
eu_stg_uri = "mongodb://mongouser:CY80!Z3%5E6T5brD3a@10.99.233.9:27017/";
eu_prod_uri = "mongodb://mongouser:r5TsO9Hd#!eqv1hx@10.99.82.22:27017/";

uri = prod_uri;

db = connect(uri + "ts_di?authSource=admin");
print('ts_di.di_pus3')
print(db.di_pus3.totalSize())
print(db.di_pus3.dataSize())
print(db.di_pus3.stats())

print('########################')

print('ts_di.di_pus4')
print(db.di_pus4.totalSize())
print(db.di_pus4.dataSize())
print(db.di_pus4.stats())

print('########################')


db = connect(uri + "ts_sensor?authSource=admin");

print('ts_sensor.sensor_pus3')
print(db.sensor_pus3.totalSize())
print(db.sensor_pus3.dataSize())
print(db.sensor_pus3.stats())

print('########################')
print('ts_sensor.sensor_pus4')
print(db.sensor_pus4.totalSize())
print(db.sensor_pus4.dataSize())
print(db.sensor_pus4.stats())

print('########################')
print('ts_sensor.sensor_pus2')
print(db.sensor_pus2.totalSize())
print(db.sensor_pus2.dataSize())
print(db.sensor_pus2.stats())

print('########################')


db = connect(uri + "ts_converter?authSource=admin");
print('ts_converter.converter_pus3')
print(db.converter_pus3.totalSize())
print(db.converter_pus3.dataSize())
print(db.converter_pus3.stats())

print('########################')
print('ts_converter.converter_pus4')

print(db.converter_pus4.totalSize())
print(db.converter_pus4.dataSize())
print(db.converter_pus4.stats())

print('########################')
print('ts_converter.converter_pus2')

print(db.converter_pus2.totalSize())
print(db.converter_pus2.dataSize())
print(db.converter_pus2.stats())

print('########################')



db = connect(uri + "ts_plc_record?authSource=admin");
print('ts_plc_record.plc_record_pus3')
print(db.plc_record_pus3.totalSize())
print(db.plc_record_pus3.dataSize())
print(db.plc_record_pus3.stats())

print('########################')
print('ts_plc_record.plc_record_pus4')

print(db.plc_record_pus4.totalSize())
print(db.plc_record_pus4.dataSize())
print(db.plc_record_pus4.stats())

print('########################')
print('ts_plc_record.plc_record_pus2')

print(db.plc_record_pus2.totalSize())
print(db.plc_record_pus2.dataSize())
print(db.plc_record_pus2.stats())

print('########################')

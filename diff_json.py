import json

def find_count_differences(input_file, output_file):
    with open(input_file, 'r') as f:
        input_data = json.load(f)

    with open(output_file, 'r') as f:
        output_data = json.load(f)

    output_map = {item['_id']: item['count'] for item in output_data}
    differences = []

    for item in input_data:
        input_id = item['_id']
        input_count = item['count']

        if input_id not in output_map:
            differences.append(f"ID '{input_id}' found in input but not in output. Input count: {input_count}")
        elif input_count > output_map[input_id]:
            diff_count = input_count - output_map[input_id]
            if diff_count > 3:
                differences.append(f"ID '{input_id}': input count ({input_count}) > output count ({output_map[input_id]}), difference: {diff_count}")

    return differences

if __name__ == "__main__":
    input_json_path = 'eps/eps_input.json'
    output_json_path = 'eps/eps_output.json'
    
    diffs = find_count_differences(input_json_path, output_json_path)
    
    if diffs:
        print("Found differences:")
        for diff in diffs:
            print(diff)
    else:
        print("No significant differences found.")
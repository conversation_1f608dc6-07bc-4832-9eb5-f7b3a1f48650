#!/usr/bin/python3
# coding=utf-8

import requests
import subprocess

if __name__ == "__main__":
    file1 = "75kWh-data_2024-01-26 06_33_58 PM.csv"
    file2 = "100kWh-data_2024-01-26 09_36_21 PM.csv"

    with open(file2) as f:
        count = 0
        for line in f:
            if line.startswith('P'):
                count = count + 1
                battery_id = line.split(',')[0].strip()

                url = f"https://api-welkin-backend.nioint.com/device/v1/battery-history/image/download?battery_id={battery_id}&start_time=1690954873366&end_time=1706506873366&image_type=1,3&record_id=all&lang=zh"
                print(url)

                response = subprocess.check_output(["curl", url, "--output", f"{battery_id}.zip"])
        print(count)
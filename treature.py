#!/usr/bin/python3
# coding=utf-8

import csv
import datetime
import requests
import pymongo
import json
import xlrd
import openpyxl
from dateutil import parser
import copy

Welkin_CN_PROD_PLC_Mongo_URI = "mongodb://mongouser:#^GThW#YdrNZVrZ)@10.130.104.42:27017/test?authSource=admin&readPreference=secondaryPreferred"
Welkin_CN_PROD_Mongo_URI = "mongodb://mongouser:#^GThW#YdrNZVrZ)@10.130.104.66:27017,10.130.104.30:27017/test?authSource=admin"
Welkin_CN_TEST_Mongo_URI = "mongodb://mongouser:mwu#5NVBjyCWZJd#@10.132.121.7:27017,10.132.121.81:27017/test?authSource=admin"

Welkin_EU_PROD_Mongo_URI = "mongodb://mongouser:r5TsO9Hd#!eqv1hx@10.138.73.83:27017/test?authSource=admin"
Welkin_EU_TEST_Mongo_URI = "mongodb://mongouser:grB*Ic4#NZe4@10.138.72.54:27017,10.138.73.166:27017,10.138.72.9:27017"
Welkin_EU_New_PROD_Mongo_URI = "mongodb://mongouser:r5TsO9Hd#!eqv1hx@10.138.24.253:27017,10.138.24.5:27017,10.138.24.124:27017/test?authSource=admin"
Welkin_EU_New_TEST_Mongo_URI = "mongodb://mongouser:CY80!Z3^6T5brD3a@10.138.73.19:27017,10.138.73.14:27017,10.138.73.64:27017/test?authSource=admin"

RedRabbit_EU_PROD_Old_Mongo_URI = "mongodb://dbadmin:grB*Ic4#<EMAIL>:27017/?authSource=admin&readPreference=primaryPreferred&appname=deployment-manager&ssl=true&retryWrites=false"
RedRabbit_EU_PROD_Mongo_URI = "mongodb://mongouser:VmIBM*es45HB1!ZS@10.138.25.124:27017,10.138.27.181:27017,10.138.27.200:27017/?authSource=admin&readPreference=primary&ssl=false"
RedRabbit_PROD_Mongo_URI = "**********************************************************************************************"

def encode_date(date):
	month = date[5:7]
	name = ''
	if month == '01' or month == '02':
		name = '1_2'
	elif month == '03' or month == '04':
		name = '3_4'
	elif month == '05' or month == '06':
		name = '5_6'
	elif month == '07' or month == '08':
		name = '7_8'
	elif month == '09' or month == '10':
		name = '9_10'
	elif month == '11' or month == '12':
		name = '11_12'
	return name

def rename_project(project):
	res = ''
	if project == 'PowerSwap':
		res = 'pus1'
	elif project == 'Power Swap':
		res = 'PowerSwap'
	elif project == 'PowerSwap2':
		res = 'pus2'
	elif project == 'Power Swap2':
		res = 'PowerSwap2'
	elif project == 'Power Swap3' or project == 'PUS3.0':
		res = 'PUS3'
	elif project == 'PUS3':
		res = 'pus3'
	return res

def read_excel(file):
	wb = xlrd.open_workbook(filename=file)
	sheet1 = wb.sheet_by_name('Sheet1')
	nrows = sheet1.nrows
	count = 0
	result = {}
	for i in range(1,nrows):
		count += 1
		row = sheet1.row_values(i)
		if result.get(row[0]) is None:
			result[row[0]] = {}
		if result[row[0]].get(row[4]) is None:
			result[row[0]][row[4]] = []
		result[row[0]][row[4]].append(str(int(row[8])))
	
	return result

class MongoClient(object):
	def __init__(self, uri, ssl=False, certs=""):
		self.uri = uri
		self.db_name = ""
		self.col_name = ""
		self.client = self._connect_mongodb(ssl, certs)

	def _connect_mongodb(self, ssl, certs):
		if ssl:
			return pymongo.MongoClient(self.uri, ssl=True, tlsCAFile=certs)
		return pymongo.MongoClient(self.uri)

	def update_user_info(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")
		data = {}
		for key in data:
			try:
				item = {
					"username": data[key],
					"user_id": key,
					"email": key+"@nio.io",
					"role": [2],
					"created_time": 1701680315905,
					"updated_time": 1701680315905
				}
				self.client[self.db_name][self.col_name].update_one({"user_id": key}, {"$set": item}, upsert=True)
			except Exception as e:
				print(item)
				continue
		print("all done!")

	def compare_realtime_data_id(self):
		records1 = self.client[self.db_name]["realtime"].find({})
		records2 = self.client[self.db_name]["realtime-test"].find({})
		old_hash = {}
		new_hash = {}
		for item in records1:
			old_hash[item["data_id"]] = 0

		unexistes = []
		for item in records2:
			if old_hash.get(item["data_id"]) is None:
				unexistes.append(item["data_id"])
				self.client[self.db_name]["realtime"].insert_one(item)
			else:
				old_hash[item["data_id"]] = 1
		invalid = []
		for key in old_hash:
			if old_hash[key] == 0:
				invalid.append(key)
				self.client[self.db_name]["realtime"].delete_one({"data_id": key})
		print("new: ", unexistes)
		print("invalid: ", invalid, len(invalid))

	def delete_invalid_oss_data_id(self):
		records = self.client[self.db_name]["realtime-oss"].find({})
		id_list = []
		for id in range(104300,104370):
			id_list.append(str(id))
		for id in range(1, 22):
			step = 1000 * id
			id_list.append(str(12+step))
			id_list.append(str(23+step))
			id_list.append(str(25+step))
			id_list.append(str(26+step))

		for item in records:
			if item.get("data_id", "xx") not in id_list:
				self.client[self.db_name]["realtime-oss"].delete_one(item)
		print("done")
		
	def get_all_collections(self):
		if self.db_name == "":
			panic("`db_name` is required!")

		collections = self.client[self.db_name].list_collection_names(session=None)
		for col in collections:
			print(col)

	def get_plc_record_collections(self):
		cols = []
		collections = self.client[self.db_name].list_collection_names(session=None)
		for col in collections:
			cols.append(col)
		return cols


	def remove_redundant_devices(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")
		records = self.client[self.db_name][self.col_name].find({})
		resourceIdMap = {}
		for item in records:
			if item.get("resource_id") is None:
				continue
			if resourceIdMap.get(item["resource_id"]) is None:
				resourceIdMap[item["resource_id"]] = item["device_id"]
			else:
				print(item["resource_id"])
			if resourceIdMap.get(item["device_id"]) is not None:
				self.client[self.db_name][self.col_name].delete_one(item)

	def get_devices_basic_infos(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")

		records = self.client[self.db_name][self.col_name].find({"online":True})
		results = []
		for item in records:
			results.append({
				"project":           self.db_name,
				"device_id":         item["_id"],
				"description":       item.get("name", ""),
				"device_supervisor": item.get("device_supervisor", ""),
				"area":              item.get("area", ""),
				"address":           item.get("address", ""),
				"region":            item.get("region", ""),
				"city_company":      item.get("city_company", ""),
				"service_state":     item.get("service_state", 0),
				"is_active":         item.get("is_active", False),
				"is_login":          item.get("is_login", False),
				"updated_time":      item.get("update_ts")
			})
		return results

	def get_PUS3_devices_basic_infos(self):
		records = self.client["oauth"]["device_basic_info"].find({"project": "PUS3", "is_active":True})
		results = {}
		for item in records:
			results[item["device_id"]] = 1

		has_factory_records = self.client["factoryData"]["device-factory-info"].find({"project": "PUS3", "state":{"$in":[2,3]}})
		has_records = {}
		res = {}
		for item in has_factory_records:
			if results.get(item["device_id"]) is not None:
				has_records[item["device_id"]] = 1 # 已激活，且已出厂
			else:
				res[item["device_id"]] = 1 # 未激活，但已经出厂
		other = {}
		for device_id in results:
			if has_records.get(device_id) is None and res.get(device_id) is None:
				other[device_id] = 1 # 已激活，但未出厂
		print("已激活且已出厂: ", has_records, len(has_records))
		print("未激活但已出厂: ", res, len(res))
		print("已激活但未出厂: ", other, len(other))


	def factory_devices_data(self):
		basic_records = self.client["oauth"]["device_basic_info"].find({"project": "PUS3", "is_active":True})
		factory_records = self.client["factoryData"]["device-factory-info"].find({"project": "PUS3", "state": {"$ne": 1}})
		results = {}
		for item in factory_records:
			results[item["device_id"]] = True
		print(results)
		count = 0
		for item in basic_records:
			count += 1
			if results.get(item["device_id"]) is None:
				print(item["device_id"])
		print(count)

	def update_device_basic_info(self, data):
		for item in data:
			try:
				self.client["factoryData"]["device-factory-info"].update_one({"project": item["project"], "device_id": item["device_id"]}, {"$set": item}, upsert=True)
			except Exception as e:
				print(item)
				continue
		print("all done!")

	def update_ps2_image_vehicle_type(self):
		db_name = "imageinfo-pus2"

		collections = self.client[db_name].list_collection_names(session=None)
		for col in collections:
			count = 0
			records = self.client[db_name][col].find({"vehicle_type": {"$exists":True,"$nin":["unknown","","NT2-ET5","NT2-ET7"]}})
			for item in records:
				if "ES" in item["vehicle_type"]:
					count += 1
					vehicle_type = item["vehicle_type"].replace("ES", "EL")
					self.client[db_name][col].update_one({"_id": item["_id"]},{"$set": {"vehicle_type": vehicle_type}})
			print(count)


	def update_ps3_image_vehicle_type(self):
		db_name = "imageinfo-pus3"

		collections = self.client[db_name].list_collection_names(session=None)
		for col in collections:
			count = 0
			records = self.client[db_name][col].find({"vehicle_type": {"$exists":True,"$nin":["unknown","","NT2-ET5","NT2-ET7"]}})
			for item in records:
				if "ES" in item["vehicle_type"]:
					count += 1
					vehicle_type = item["vehicle_type"].replace("ES", "EL")
					if vehicle_type.startswith("EL"):
						vehicle_type = "NT1-" + vehicle_type
					self.client[db_name][col].update_one({"_id": item["_id"]},{"$set": {"vehicle_type": vehicle_type}})
			print(count)

			



	def drop_one_index(self, index_name):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")

		try:
			self.client[self.db_name][self.col_name].drop_index(index_name)
		except Execption as e:
			print(e)

	def import_data_from_json_file(self, file_path):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")

		for line in open(file_path):
			item = json.loads(line)
			try:
				self.client[self.db_name][self.col_name].update_one({"data_id": item["data_id"]}, {"$set": item}, upsert=True)
			except Exception as e:
				print(item)
				continue
		print("all done!")

	def import_data_from_json_file_2(self, file_path):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")

		with open(file_path, 'r') as f:
			f_data = json.load(f)
			for item in f_data:
				try:
					self.client[self.db_name][self.col_name].update_one({"data_id": item["data_id"]}, {"$set": item}, upsert=True)
				except Exception as e:
					print(item)
		print("all done!")

	def get_service_info(self, request_id):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")

		col = self.client[self.db_name][self.col_name]
		record = col.find_one({"request_id": request_id})
		infos = record["servicesinfo"]
		results = []
		for item in infos:
			results.append({"service_id": item["service_id"], "start_time": item["start_time"], "end_time": item["end_time"]})
		print(results)


	def get_torque_report(self, device_id):
		records = self.client[self.db_name][self.col_name].find({"device_id":device_id}).sort("test_report_gen_time", 1)
		result = {}
		for item in records:
			print(item.get("insert_ts"))
			result = {
				"requestId": item.get("request_id"),
				"deviceId": device_id,
				"deviceType": "PUS3",
				"serviceLst": item.get("servicesinfo"),
			}
			print(json.dumps(result))
			break
		return

	def get_torque_serviceinfo(self, device_id, request_id):
		records = self.client[self.db_name][self.col_name].find({"device_id":device_id,"request_id":request_id})
		result = {}
		for item in records:
			result = {
				"request_id": request_id,
				"service_info": item["servicesinfo"],
			}
			break
		print(json.dumps(result))


	def get_torque_feature_calculation(self, request_id,device_id):
		records = self.client[self.db_name][self.col_name].find({"request_id":request_id,"device_id":device_id},{"_id":0})
		return records

	def insert_torque_feature_calculation(self, records):
		try:
			self.client[self.db_name][self.col_name].insert_many(records)
		except Execption as e:
			print(e)

	def get_plc_record_by_devices(self):
		if self.db_name == "":
			panic("`db_name` is required!")

		collections = self.client[self.db_name].list_collection_names(session=None)
		for col in collections:
			count = 0
			records = self.client[self.db_name][col].find({"timestamp":{"$gte":1688745600000,"$lt":1688785200000}})
			for item in records:
				count += 1
				if count > 1:
					break
			if count == 0:
				print(col)

	def get_battery_demands(self):
		if self.db_name == "":
			panic("`db_name` is required!")

		records = self.client[self.db_name][self.col_name].find({"battery_demand":{"$exists":True}},{"battery_demand":1, "device_id":1})
		result = {}
		for item in records:
			for demand in item["battery_demand"]:
				if demand["70"] == 0 or demand["100"] == 0:
					print(item)
					break
		return

	def update_device2cloud_realtime(self):
		data_id_table = {
			"充电": {
				"系统参数": [605180,605182,605132,605183,605184,605135,605139,605133,605134,605185,605186,605187,605137,605138],
				"1#电池基本参数": [501108,501134,605140,501105,605188],
				"2#电池基本参数": [502108,502134,605141,502105,605189],
				"3#电池基本参数": [503108,503134,605142,503105,605190],
				"4#电池基本参数": [504108,504134,605143,504105,605191],
				"5#电池基本参数": [505108,505134,605144,505105,605192],
				"6#电池基本参数": [506108,506134,605145,506105,605193],
				"7#电池基本参数": [507108,507134,605146,507105,605194],
				"8#电池基本参数": [508108,508134,605147,508105,605195],
				"9#电池基本参数": [509108,509134,605148,509105,605196],
				"10#电池基本参数": [510108,510134,605149,510105,605197],
				"11#电池基本参数": [511108,511134,605150,511105,605198],
				"12#电池基本参数": [512108,512134,605151,512105,605199],
				"13#电池基本参数": [513108,513134,605152,513105,605200],
			}
		}

		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")
		for key1 in data_id_table:
			for key2 in data_id_table[key1]:
				try:
					data_id_list = []
					for item in data_id_table[key1][key2]:
						data_id_list.append(str(item))
					self.client[self.db_name][self.col_name].update_many({"data_id": {"$in":data_id_list}}, {"$set": {"category":key2}}, upsert=True)
				except Exception as e:
					print(e)

	def update_realtime2_column(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")
		records = self.client[self.db_name][self.col_name].find({})
		for item in records:
			update = {}
			if item["subsystem"] == "充电":
				update["subsystem"]
			try:
				self.client[self.db_name][self.col_name].update_one({"data_id": item["data_id"]}, {"$set": {"var_cn_name":item["description"]}}, upsert=True)
			except Exception as e:
				print(e)


	def drop_realtime2_column(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")
		records = self.client[self.db_name][self.col_name].find({})
		for item in records:
			try:
				self.client[self.db_name][self.col_name].update_one({"data_id": item["data_id"]}, {"$unset": {"description":""}}, upsert=False)
			except Exception as e:
				print(e)
		try:
			self.client[self.db_name][self.col_name].update_many({}, {"$unset": {"description":""}}, upsert=False,multi=True)
		except Exception as e:
			print(e)


	def update_device2cloud_realtimeall(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")
		records = self.client[self.db_name][self.col_name].find({})
		for item in records:
			try:
				self.client[self.db_name][self.col_name].update_one({"_id": item["_id"]}, {"$set": {"var_type": item["var_type"].strip()}})
			except Exception as e:
				print(e)

	def update_device2cloud_realtime_category(self):
		data_id_list = []
		for i in range(190200,190238):
			data_id_list.append(str(i))
		print(data_id_list)
		try:
			self.client[self.db_name][self.col_name].update_many({"data_id": {"$in": data_id_list}}, {"$set":{"category": "伺服"}})
		except Exception as e:
			print(e)

	def update_alarm_basic_info(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")

		records = self.client[self.db_name][self.col_name].find({})
		for item in records:
			if item.get("data_id") is None or item["data_id"] == "":
				self.client[self.db_name][self.col_name].delete_one(item)
			try:
				subsystem = item.get("subsystem", "").strip()
				data_id = item["data_id"].strip()
				var_cn_name = item["var_cn_name"].strip()
				var_en_name = item["var_en_name"].strip()
				local_tips = item.get("local_tips", "").replace("\n", " ")
				alarm_source = item.get("alarm_source", "").strip()
				self.client[self.db_name][self.col_name].update_one({"_id": item["_id"]}, {"$set": {"subsystem": subsystem, "data_id": data_id, "var_cn_name": var_cn_name, "var_en_name": var_en_name, "local_tips": local_tips, "alarm_source":alarm_source}})
			except Exception as e:
				print(item)
				print(e)
				continue
		print("all done!")

	def update_realtime2_oss_category(self, records):
		for item in records:
			try:
				self.client[self.db_name][self.col_name].update_one({"data_id": item["data_id"]}, {"$set": {"category": item["category"]}})
			except Exception as e:
				print(e)


	def update_en_name(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")
	
		dataIdMap = {
			"1047": "Grid Frequency",
			"605132": "Min Power COE",
			"605133": "Max System Charge Power",
			"605134": "Max system Discharge Power",
			"605135": "Remote Control Enable",
			"605137": "Remote Distribute Power",
			"605138": "Station Power Limit",
			"605139": "Station Use Power",
			"605180": "Power Schedule Mode",
			"605182": "Protocol_104",
			"605183": "Number of 70# Battery Reserved",
			"605184": "Number of 100# Battery Reserved",
			"605185": "Base Line Power",
			"605186": "Max Allocated Energy of Charging",
			"605187": "Max Allocated Energy of Discharging"
		}

		for index in range(1,14):
			dataIdMap[str(605139+index)] = str(index) + "# Branch Distribute Power"
			dataIdMap[str(605187+index)] = str(index) + "# Branch Limit Power"
			dataIdMap[str(500010+index*1000)] = str(index) + "# Branch Work Status"
			dataIdMap[str(500156+index*1000)] = str(index) + "# Battery Pack Capacity"
			dataIdMap[str(500105+index*1000)] = str(index) + "# Battery Pack Power"
			dataIdMap[str(500108+index*1000)] = str(index) + "# BMS User Soc"
			dataIdMap[str(500134+index*1000)] = str(index) + "# Long-term Charging Power Limit"

		records = self.client[self.db_name][self.col_name].find({})
		for item in records:
			if dataIdMap.get(item["data_id"]) is not None:
				try:
					self.client[self.db_name][self.col_name].update_one({"data_id": item["data_id"]}, {"$set": {"var_en_name": dataIdMap[item["data_id"]]}})
				except Exception as e:
					print(e)
			else:
				try:
					self.client[self.db_name][self.col_name].update_one({"data_id": item["data_id"]}, {"$set": {"var_en_name": ""}})
				except Exception as e:
					print(e)

	def update_alarm_subsystem_pus3(self):
		if self.db_name == "" or self.col_name == "":
			panic("`db_name` and `col_name` are required!")

		records = self.client[self.db_name][self.col_name].find({})
		for item in records:
			if item.get("data_id") is None:
				self.client[self.db_name][self.col_name].delete_one(item)
			try:
				subsystem = item.get("subsystem", "").strip()
				local_tips = item.get("local_tips", "").replace("\n", "")
				self.client[self.db_name][self.col_name].update_one({"_id": item["_id"]}, {"$set": {"subsystem": subsystem, "local_tips": local_tips}})
			except Exception as e:
				print(item)
				continue
		print("all done!")

	def insert_new_data_charging(self):
		dataList = ["104300","104302","104304","104306","104308","104310","104312","104314","104316","104318","104320","104322","104324","104326","104328","104330","104332","104334","104336","104338","104340","104342","104344","104346","104348","104350","104352","104354","104356","104358","104360","104362","104364","104366","104368","104301","104303","104305","104307","104309","104311","104313","104315","104317","104319","104321","104323","104325","104327","104329","104331","104333","104335","104337","104339","104341","104343","104345","104347","104349","104351","104353","104355","104357","104359","104361","104363","104365","104367","104369"]
		for d in dataList:
			records = self.client[self.db_name]["realtime"].find({"data_id": d})
			for item in records:
				try:
					self.client[self.db_name][self.col_name].insert_one(item)
					# self.client[self.db_name]["realtime"].delete_one(item)
				except Exception as e:
					print(e)
		print("all done!")

	def update_cn_name(self):
		count = 0
		for i in range(100015,100886,30):
			count += 1
			try:
				print(i)
				records = self.client[self.db_name][self.col_name].find({"data_id": str(i)})
				for item in records:
					var_cn_name = item["var_cn_name"].replace("空气","进风口")
					self.client[self.db_name][self.col_name].update_one({"data_id": str(i)}, {"$set": {"var_cn_name": var_cn_name}})
			except Exception as e:
				print(item)
				continue
		print(count)

	def transfer_service_battery(self):
		time_list = ['2023-12-01', '2023-12-02', '2023-12-03', '2023-12-04']
		
		for time in time_list:
			print(f'create_time {time} start')
			count = self.client[self.db_name]['service'].count_documents({"create_time": time})
			limit = count // 2 + 1
			skip = [0, limit]
			for sp in skip:
				with self.client[self.db_name]['service'].find({"create_time": time}, no_cursor_timeout=True).skip(sp).limit(limit) as cursor:
					for item in cursor:
						del item['_id']
						project = item.get("project")
						service_id = item.get("service_id")
						create_time = item.get("create_time")
						if project == None or service_id == None or create_time == None: 
							print(f"invalid item: {item}")
							continue
						print(f'{service_id}')
						try:
							record = self.client[f'serviceinfo-{rename_project(project)}'][encode_date(create_time)].find({"service_id": service_id}).next()
						except Exception as e:
							print(e)
							continue
						ev_battery_id = record.get("ev_battery_id")
						service_battery_id = record.get("service_battery_id")
						ev_id = record.get("ev_id")
						if ev_battery_id != None and ev_battery_id != '':
							item['ev_battery_id'] = ev_battery_id
						if service_battery_id != None and service_battery_id != '':
							item['service_battery_id'] = service_battery_id
						if ev_id != None and ev_id != '':
							item['ev_id'] = ev_id
						try:
							self.client[self.db_name][self.col_name].update_one({"service_id": service_id}, {"$set": item}, upsert=True)
						except Exception as e:
							print(e)
							continue
			print(f'create_time {time} done')
		print('done')
	
	def get_collection_size(self):
		collections = self.client[self.db_name].list_collection_names(session=None)
		print(len(collections))
		size = 0
		for col in collections:
			size += self.client[self.db_name].command('collstats', col)['size']
		print(f'collection size: {size/1024/1024} MB')

	def get_collection_name(self):
		collections = self.client[self.db_name].list_collection_names(session=None)
		colstr = " ".join(['"' + col + '"' for col in collections])
		print("(" + colstr + ")")		

	def get_index_size(self):
		db = self.client[self.db_name]
		collections = db.list_collection_names(session=None)
		size = 0
		for col in collections:
			size += db.command("collStats", col)["totalIndexSize"]
		print(f'{size/1024/1024} MB')

	def get_plc_count(self):
		db = self.client[self.db_name]
		collections = db.list_collection_names(session=None)
		for col in collections:
			count = db[col].count_documents({"date": {"$lt": datetime.datetime.fromtimestamp(1691489404)}})
			if count > 0:
				print(f'{col}: {count}')

	def get_collection_empty_size(self):
		db = self.client[self.db_name]
		collections = db.list_collection_names(session=None)
		print(len(collections))
		size = 0
		for col in collections:
			col_size = db.command('collstats', col)['wiredTiger']['block-manager']['file bytes available for reuse']
			print(f'{col} free size: {col_size/1024/1024} MB')
			size += col_size
		print(f'db free size: {size/1024/1024} MB')

	def delete_invalid_lpr(self):
		self.client[self.db_name][self.col_name].delete_many({"project": ""})

	def list_image_type29(self):
		db = self.client[self.db_name]
		collections = db.list_collection_names(session=None)
		print(len(collections))
		for col in collections:
			cnt = self.client[self.db_name][col].count_documents({"image_type": 29})
			if cnt > 0:
				print(col, cnt)

	def sync_alarm_level(self):
		db = self.client['device2cloud']
		coll_alarm_pus4 = "alarm-pus4"
		coll_stuck_alarm = "stuck-alarm"
		records = db[coll_stuck_alarm].find({'project': "PUS4"})
		success_cnt = 0
		for item in records:
			project = item.get("project")
			alarm_id = item.get("alarm_id")
			if project is None or alarm_id is None:
				print(f"project or alarm_id is none, {item}")
				continue
			coll = ""
			if project == "PUS4":
				coll = coll_alarm_pus4
			else:
				print(f"project is not PUS4, {project}")
			record = db[coll].find_one({"data_id": alarm_id})
			if record is None:
				record = db[coll].find_one({"data_id": "'"+alarm_id})
				if record is None:
					print(f"alarm record is none, {item}")
					continue
			alarm_level = record.get("alarm_level")
			if alarm_level is None:
				print(f"alarm_level is none, {record}")
				continue
			db[coll_stuck_alarm].update_one({"project": project, "alarm_id": alarm_id}, {"$set": {"alarm_level": alarm_level}})
			success_cnt += 1
		print(f"success cnt: {success_cnt}")

	def update_date(self):
		collection = self.client[self.db_name][self.col_name]
		filter = {"date":{"$regex":"2024"}}
		res = collection.distinct("date", filter=filter)

		for day in res:
			date_object = datetime.datetime.strptime(day, "%Y-%m-%d")
			print(day, date_object)
			collection.update_many({"date":day}, {"$set": {"date": date_object}})
		# update = {
		# 	"$set": {
		# 		"date": {
		# 			"$dateFromString": {
		# 				"dateString": "$day", 
		# 				"format": "%Y%m%d"
		# 			}
		# 		}
		# 	}
		# }
		# collection.update_many(, update)

	def distinct_device(self):
		collection = self.client[self.db_name][self.col_name]
		res = collection.distinct("device_id")
		print(res)
	
	def find_unexpected_devices(self, device_list):
		collection = self.client[self.db_name][self.col_name]
		not_found = 0
		for device in device_list:
			res = collection.find_one({"device_id": device})
			if res is None:
				not_found += 1
				print(f"not find device: {device}")
		print(f'device_list: {len(device_list)}, not found devices: {not_found}')

	def update_psos_battery_config_in_simulation(self):
		collection = self.client[self.db_name][self.col_name]
		res = collection.find({})
		count = 0
		for item in res:
			battery_config_origin = item['device_info']['operation_strategy_info']['battery_type_conf']
			battery_config = [0.0, 0.0, 0.0, 0.0]
			for battery in item['device_info']['operation_strategy_info']['battery_info']:
				if battery.get('battery_rated_kwh') == 50:
					battery_config[0] += 1
				elif battery.get('battery_rated_kwh') == 75:
					battery_config[1] += 1
				elif battery.get('battery_rated_kwh') == 100:
					battery_config[2] += 1
				elif battery.get('battery_rated_kwh') == 150:
					battery_config[3] += 1
			same = True
			for i in range(0, 4):
				if int(battery_config[i]) != int(battery_config_origin[i]):
					same = False
			if not same:
				count += 1
				collection.update_one({'_id': item['_id']}, {'$set': {'device_info.operation_strategy_info.battery_type_conf': battery_config}})
				print(f"_id: {item['_id']}, battery_type_conf: {battery_config_origin}, battery info: {battery_config}")
		print(count)

	def update_camera_data(self, project):
		self.db_name = "camera_management"
		self.col_name = f'{project}_device_camera_data'
		collection = self.client[self.db_name][self.col_name]
		devices = collection.distinct("device_id")
		for device_id in devices:
			area_record = collection.find_one({"device_id": device_id, "area": {"$exists": True}})
			collection.update_many({"device_id": device_id}, {"$set": {"area": area_record["area"]}})
		
	def psos_find_empty_day(self):
		collection = self.client[self.db_name][self.col_name]
		start_day_str = "2024-06-15 00:00:00"
		start_day = datetime.datetime.strptime(start_day_str, "%Y-%m-%d %H:%M:%S")
		delta = datetime.timedelta(days=1)
		while start_day < datetime.datetime.today():
			cnt = collection.count_documents({'date': {'$gte': start_day, '$lt': start_day+delta}})
			if cnt == 0:
				print(start_day)
			start_day += delta

	def find_empty_camera(self, project):
		expected = 2
		if project == 'pus3':
			expected = 4
		elif project == 'pus4':
			expected = 4
		db_service_name = f'serviceinfo-{project}'
		db_image_name = f'imageinfo-{project}'
		col_service_name = '9_10'
		collection_service = self.client[db_service_name][col_service_name]
		start_day_str = "2024-10-07 16:00:00"
		start_day = datetime.datetime.strptime(start_day_str, "%Y-%m-%d %H:%M:%S")
		end_day = start_day+datetime.timedelta(days=1)
		cursor = collection_service.aggregate([{'$match': {'date': {'$gte': start_day, '$lt': end_day}, 'finish_result':1}}, {'$group': {'_id': {'device_id': '$device_id', 'service_id': '$service_id'}}}, {'$project': {'device_id': '$_id.device_id', 'service_id': '$_id.service_id', '_id': 0}}])
		device_dic = {}
		for record in cursor:
			if device_dic.get(record['device_id']) is None:
				device_dic[record['device_id']] = []
			device_dic[record['device_id']].append(record['service_id'])
		cursor.close()

		f = open(f'services-{project}-{20241008}.txt', 'a', encoding='utf-8')

		device_cnt = 0
		for device, services in device_dic.items():
			collection_image = self.client[db_image_name][device]
			service_cnt = 0
			for service in services:
				cnt = collection_image.count_documents({'service_id': service, 'image_type': {'$in': [1,3]}})
				if cnt < expected:
					f.write(f'{device}\t{service}\timage_count:{cnt}\n')
					service_cnt += 1
			if service_cnt > 0:
				device_cnt += 1
				f.write(f'设备:{device} 订单总数:{len(services)} 缺失订单数:{service_cnt}\n')
				f.write('\n')
		f.write(f'设备总数:{len(device_dic)} 缺失设备数:{device_cnt}\n')
		f.close()

	def refresh_camera_data(self, project):
		proj = ""
		if project == "pus2":
			proj = "PowerSwap2"
		elif project == "pus3":
			proj = "PUS3"
		elif project == "pus4":
			proj = "PUS4"
		db_camera = "camera_management"
		db_image = f"imageinfo-{project}"
		col_camera = f'{project}_device_camera_data'
		col_camera_info = f'{project}_camera_info'
		collection_camera_info = self.client[db_camera][col_camera_info]
		camera_map = {}
		camera_list = []
		cursor = collection_camera_info.find({"algorithm": {"$exists": True}})
		for item in cursor:
			camera_map[item["camera_type"]] = item["algorithm"]
			camera_list.append(item["camera_type"])
		print('camera map: ', camera_map)
		collection_camera = self.client[db_camera][col_camera]
		cursor = collection_camera.aggregate([{"$match": {"has_image": True, "camera_type": {"$in": camera_list}}}, {"$group": {"_id": "$device_id", "camera_list": {"$push": "$camera_type"}}}, {"$project": {"_id": 1, "camera_list": 1}}])
		header = {'Content-Type': 'application/json'}
		camera_cursor = []
		for record in cursor:
			camera_cursor.append(record)
		for record in camera_cursor:
			print(record)
			collection_image = self.client[db_image][record['_id']]
			for camera_type in record['camera_list']:
				try:
					image = collection_image.find({"image_type": 29, "camera_type": camera_type}, {"image_url": 1, "_id": 0}).sort([("image_gen_time", -1)]).limit(1).next()
				except Exception as e:
					print(e)
					continue
				data = {
					"image_url": image["image_url"],
					"algorithm": camera_map[camera_type],
					"project": proj
				}
				resp = requests.post("https://api-welkin-algorithm.nioint.com/cc/v1/cc-calculation", headers=header, data=json.dumps(data))
				try:
					resp = resp.json()
					print(record['_id'], camera_type, resp)
					collection_camera.update_one({"device_id": record['_id'], "camera_type": camera_type}, {"$set": {
						"predict_type":   resp["predict_type"],
						"predict_result": resp["predict_result"]+1,
						"predict_ts":     int(datetime.datetime.now().timestamp()*1000),
					}})
				except Exception as e:
					print(e)
		print("done")

	def psos_config(self):
		self.db_name = 'algorithm'
		self.col_name = 'psos_configs'
		collection = self.client[self.db_name][self.col_name]
		cursor = collection.find({"config_name_origin": "24国庆"})
		for item in cursor:
			print(item['device_info']['device_id'])
			record =  collection.update_one({"config_name_origin": "24国庆_满电池", "device_info.device_id": item['device_info']['device_id']}, {"$set": {"service_info.swapping_user_list": item['service_info']['swapping_user_list']}})
		
	def refresh_alarm(self, project):
		print(project)
		self.db_name = 'alarminfo'
		collection_device = self.client['oauth']['device_basic_info']
		cursor = collection_device.find({"project": project, "resource_id": {"$exists": True}})
		resource_2_real = {}
		for record in cursor:
			resource_2_real[record.get('resource_id')] = record['device_id']
		for i in range(1, 13):
			print(i)
			self.col_name = f'{project.lower()}_{i}'
			collection = self.client[self.db_name][self.col_name]
			device_list = collection.distinct("device_id")
			for device_id in device_list:
				if device_id in resource_2_real:
					print(device_id)
					err = collection.update_many({"device_id": device_id}, {"$set": {"device_id": resource_2_real[device_id]}})
					print(err)

	def trim_battery(self):
		self.db_name = "serviceinfo"
		for colname in ["pus2", "pus3", "pus4"]:
			print(colname)
			collection = self.client[self.db_name][colname]
			cursor = collection.find({})
			for record in cursor:
				ev_battery_id = record.get('ev_battery_id')
				service_battery_id = record.get('service_battery_id')
				if ev_battery_id is None or service_battery_id is None:
					continue
				ev_battery_id_new = ev_battery_id.strip()
				service_battery_id_new = service_battery_id.strip()
				if ev_battery_id == ev_battery_id_new and service_battery_id == service_battery_id_new:
					continue
				print(record["service_id"])
				collection.update_one({"service_id": record["service_id"]}, {"$set": {"ev_battery_id": ev_battery_id_new, "service_battery_id": service_battery_id_new}})

	def sync_stuck(self):
		resp = requests.get("https://api-welkin-backend.nioint.com/diagnosis/v1/stuck/service?start_time=1725120000000&end_time=1727343230000&project=PowerSwap2&page=1&size=99999&lang=zh")
		resp_dic = json.loads(resp.text)
		service_id_list = []
		for item in resp_dic['data']:
			service_id_list.append(item['service_id'])
		print(f'service2: {len(service_id_list)}')
		collection2 = self.client['serviceinfo']['pus2']
		# count = collection2.count_documents({"service_id": {"$in": service_id_list}})
		res = collection2.update_many({"service_id": {"$in": service_id_list}}, {"$set": {"is_stuck": True}})
		print(res)

		resp = requests.get("https://api-welkin-backend.nioint.com/diagnosis/v1/stuck/service?start_time=1725120000000&end_time=1727343230000&project=PUS3&page=1&size=99999&lang=zh")
		resp_dic = json.loads(resp.text)
		service_id_list = []
		for item in resp_dic['data']:
			service_id_list.append(item['service_id'])
		print(f'service3: {len(service_id_list)}')
		collection3 = self.client['serviceinfo']['pus3']
		# count = collection3.count_documents({"service_id": {"$in": service_id_list}})
		res = collection3.update_many({"service_id": {"$in": service_id_list}}, {"$set": {"is_stuck": True}})
		print(res)

	def sync_stuck_ev_type(self):
		collection = self.client['diagnosis-management']['stuck-service']
		collection2 = self.client['serviceinfo']['pus2']
		collection3 = self.client['serviceinfo']['pus3']
		cursor = collection.find({"service_start_time":{"$gte": 1726156800000}, "ev_type": {"$exists": False}})
		for record in cursor:
			print(record['project'], record['service_id'])
			coll = collection2
			if record['project'] == 'PUS3':
				coll = collection3
			item = coll.find_one({'service_id': record['service_id']})
			if item is None:
				print('service is none')
				continue
			ev_type = item.get('ev_type')
			if ev_type is None:
				print('ev_type is none')
				continue
			update = {'$set': {'ev_type': item['ev_type']}}
			collection.update_one({'service_id': record['service_id']}, update)

	def delete_outdate_data(self):
		collections = self.client[self.db_name].list_collection_names(session=None)
		for coll_name in collections:
			collection = self.client[self.db_name][coll_name]
			collection.drop()
			print(coll_name)

	def list_image_to_csv(self):
		db = self.client[self.db_name]
		collections = db.list_collection_names(session=None)
		print(len(collections))
		with open(f'{self.db_name}.csv', 'w', newline='') as csvfile:
			writer = csv.writer(csvfile)
			for col in collections:
				if col == 'delete-info': continue
				rows = []
				cursor = self.client[self.db_name][col].find({"image_type": 20, "image_gen_time": {"$gte": 1698768000000, "$lt": 1711900800000}})
				for record in cursor:
					rows.append([record['device_id'], record['image_url']])
				writer.writerows(rows)
				print(col)

	def fix_health(self):
		collection = self.client['algorithm']['health_data']
		cursor = collection.find({'day': 1729872000000})
		mp = {}
		for item in cursor:
			if item.get("sensor_data") is not None:
				mp[item['device_id']] = item['sensor_data']
		cursor = collection.find({'day': 1729958400000})
		for item in cursor:
			if mp.get(item['device_id']) is not None:
				collection.update_one({'device_id': item['device_id'], 'day': 1729958400000}, {'$set': {'sensor_data': mp[item['device_id']]}})

	def oplog_op_to_csv(self, start, end, threshold):
		db = self.client['op-log-pus2']
		collections = db.list_collection_names(session=None)
		start_ts = int(datetime.datetime.strptime(start, '%Y-%m-%d').timestamp() * 1000)
		end_ts = int(datetime.datetime.strptime(end, '%Y-%m-%d').timestamp() * 1000)
		with open(f'op-log-pus2_{start}_{end}_{threshold}min.csv', 'w', newline='') as csvfile:
			writer = csv.writer(csvfile)
			for col in collections:
				cursor = db[col].find({'page': {'$regex': '维护'}, 'timestamp': {'$gte': start_ts, '$lt': end_ts}}, sort=[('timestamp', 1)])
				start_time = 0
				prev_time = 0
				op_records = {}
				for item in cursor:
					if start_time == 0:
						start_time = item['timestamp']
						prev_time = start_time
						continue
					if 0 < item['timestamp'] - prev_time < threshold * 60 * 1000:
						op_records[start_time] = {}
					else:
						start_time = item['timestamp']
					prev_time = item['timestamp']
				if len(op_records) == 0:
					continue
				rows = []
				print(col, len(op_records))
				for ts in op_records:
					readable_time = datetime.datetime.fromtimestamp(ts / 1000).strftime('%Y-%m-%d %H:%M:%S')
					rows.append([readable_time, col])
				writer.writerows(rows)

		db = self.client['op-log-pus3']
		collections = db.list_collection_names(session=None)
		start_ts = int(datetime.datetime.strptime(start, '%Y-%m-%d').timestamp() * 1000)
		end_ts = int(datetime.datetime.strptime(end, '%Y-%m-%d').timestamp() * 1000)
		with open(f'op-log-pus3_{start}_{end}_{threshold}min.csv', 'w', newline='') as csvfile:
			writer = csv.writer(csvfile)
			for col in collections:
				cursor = db[col].find({'type': 1, 'operation': {'$gte': 4001, '$lt': 4090}, 'timestamp': {'$gte': start_ts, '$lt': end_ts}}, sort=[('timestamp', 1)])
				start_time = 0
				prev_time = 0
				op_records = {}
				for item in cursor:
					if start_time == 0:
						start_time = item['timestamp']
						prev_time = start_time
						continue
					if 0 < item['timestamp'] - prev_time < threshold * 60 * 1000:
						op_records[start_time] = {}
					else:
						start_time = item['timestamp']
					prev_time = item['timestamp']
				if len(op_records) == 0:
					continue
				rows = []
				print(col, len(op_records))
				for ts in op_records:
					readable_time = datetime.datetime.fromtimestamp(ts / 1000).strftime('%Y-%m-%d %H:%M:%S')
					rows.append([readable_time, col])
				writer.writerows(rows)

	# 批量修改用户权限
	def oauth(self):
		collection = self.client['oauth']['user_info']
		# with open(f'insert.csv', 'r', encoding='utf-8') as csvfile:
		# 	reader = csv.reader(csvfile)
		# 	header = next(reader)
		# 	current_time = datetime.datetime.now().timestamp()
		# 	current_timestamp = int(current_time * 1000)
		# 	# 循环遍历 CSV 文件中的每一行
		# 	for row in reader:
		# 		print('Row:', row)
		# 		record = {
		# 			'username': row[3],
		# 			'user_id': row[5],
		# 			'department': '',
		# 			'role': [1],
		# 			'created_time': current_timestamp,
		# 			'updated_time': current_timestamp,
		# 		}
		# 		collection.update_one({'user_id': row[5]}, {'$set': record}, upsert=True)
		
		with open(f'update.csv', 'r', encoding='utf-8') as csvfile:
			reader = csv.reader(csvfile)
			header = next(reader)
			current_time = datetime.datetime.now().timestamp()
			current_timestamp = int(current_time * 1000)
			user_ids = []
			for row in reader:
				print('Row:', row)
				record = {
					'username': row[2],
					'user_id': row[3],
					'department': '',
					'role': [1],
					'updated_time': current_timestamp,
				}
				collection.update_one({'user_id': row[3]}, {'$set': record}, upsert=True)

	def get_cc(self):
		db = self.client['imageinfo-pus4']
		collections = db.list_collection_names(session=None)
		for col in collections:
			count = db[col].count_documents({"image_type": 29, "extra_data": {"$exists": True}})
			if count > 0:
				print(col, count)

	def update_torque_services(self):
		collection = self.client['factoryData']['torque-report']
		record = collection.find_one({'request_id': "cqoqv8qs6rahm6n8obm0"})
		service_info = record['servicesinfo']
		for i, _ in enumerate(service_info):
			if i < 80:
				service_info[i]['ev_brand'] = 'NIO'
			else:
				service_info[i]['ev_brand'] = 'ONVO'
		print(service_info)
		collection.update_one({'request_id': "cqoqv8qs6rahm6n8obm0"}, {'$set': {'servicesinfo': service_info}})

	# 电池类型
	def convert_battery_type(self, originType):
		if originType == 0 or originType == 1 or originType == 3:
			return 70, 70
		elif originType == 2:
			return 84, 84
		elif originType == 4:
			return 84, 70
		elif originType == 6 or originType == 13:
			return 100, 100
		elif originType == 7:
			return 100, 70
		elif originType == 8 or originType == 12:
			return 75, 75
		elif originType == 9:
			return 100, 84
		elif originType == 10:
			return 150, 150
		elif originType == 11 or originType == 14:
			return 100, 75
		elif originType == 16:
			return 50, 50
		elif originType == 17:
			return 70, 50
		elif originType == 73:
			return 60, 60
		elif originType == 68:
			return 75, 75
		return -1, -1

	def satisfy_csv(self, start, end):
		start_ts = int(datetime.datetime.strptime(start, '%Y-%m-%d').timestamp() * 1000)
		end_ts = int(datetime.datetime.strptime(end, '%Y-%m-%d').timestamp() * 1000)
		backend_mongo = MongoClient(Welkin_CN_PROD_Mongo_URI)
		plc_mongo = MongoClient(Welkin_CN_PROD_PLC_Mongo_URI)

		# 告警基本信息
		alarm_info = {}
		alarm_ps2 = backend_mongo.client['device2cloud']['alarm-powerswap2'].find({'data_id': {'$exists': True}})
		for alarm in alarm_ps2:
			alarm_info[alarm['data_id']] = alarm
		alarm_pus3 = backend_mongo.client['device2cloud']['alarm-pus3'].find({'data_id': {'$exists': True}})
		for alarm in alarm_pus3:
			alarm_info[alarm['data_id']] = alarm
		alarm_pus4 = backend_mongo.client['device2cloud']['alarm-pus4'].find({'data_id': {'$exists': True}})
		for alarm in alarm_pus4:
			alarm_info[alarm['data_id']] = alarm

		collection_satisfy = plc_mongo.client['serviceinfo']['satisfy_data']
		cursor = collection_satisfy.find({"comment_time": {"$gte": start_ts, "$lt": end_ts}, "service_id": {"$exists": True}})
		satisfy_data = {}
		service_data = {}
		order_data = {}
		service_ids = {}

		for item in cursor:
			if item.get('service_id') is None or item.get('project') is None:
				continue
			satisfy_data[item['service_id']] = {
				"comment": item.get('comment'),
				"comment_time": item.get('comment_time'),
				"device_id": item.get('device_id'),
				'is_valid': item.get('is_valid'),
				'order_id': item.get('order_id'),
				'project': item.get('project'),
				'reason': item.get('reason'),
				'score': item.get('score'),
				'solution': item.get('solution'),
				'user_tag': item.get('user_tag'),
				'ev_battery_id': item.get('ev_battery_id'),
				'diagnosis_tag': item.get('diagnosis_tag'),
				'ev_id': item.get('ev_id'),
				'ev_type': item.get('ev_type'),
				'service_battery_id': item.get('service_battery_id')
			}
			if service_ids.get(item["project"]) is None:
				service_ids[item["project"]] = []
			service_ids[item["project"]].append(item['service_id'])

		for project, slist in service_ids.items():
			coll_project = ''
			if project == 'PowerSwap2':
				coll_project = 'pus2'
			elif project == "PowerSwap":
				coll_project = 'pus1'
			elif project == 'PUS3':
				coll_project = 'pus3'
			elif project == 'PUS4':
				coll_project = 'pus4'
			services = backend_mongo.client['serviceinfo'][coll_project].find({'service_id': {"$in": slist}})
			for item in services:
				service_data[item['service_id']] = {
					'ev_battery_original_type': item.get('ev_battery_original_type'),
					'ev_battery_real_soc': item.get('ev_battery_real_soc'),
					'ev_battery_soc': item.get('ev_battery_soc'),
					'service_start_time': item.get('service_start_time'),
					'service_battery_original_type': item.get('service_battery_original_type'),
					'service_battery_real_soc': item.get('service_battery_real_soc'),
					'service_battery_soc': item.get('service_battery_soc'),
					'service_end_time': item.get('service_end_time'),
				}

			orders = plc_mongo.client['serviceinfo'][f'order_info_full_{project.lower()}'].find({'service_id': {"$in": slist}})
			for item in orders:
				order_data[item['service_id']] = {
					'order_start_time': item.get('order_start_time'),
					'order_end_time': item.get('order_end_time'),
					'electricity_kwh': item.get('electricity_kwh'),
				}
		# satisfy data:  {'PS-NIO-68e3c25b-a862cbbfe433fc8b69534cf7848b293b7bd540e61730734680250': {'comment': '', 'comment_time': 1730735990000, 'device_id': 'PS-NIO-68e3c25b-a862cbbf', 'is_valid': True, 'order_id': '825851247534549676', 'project': 'PowerSwap2', 'reason': None, 'score': 5, 'solution': None, 'user_tag': ['专员态度很好', '换电环境很好', '支付很顺畅', '换电顺利无故障', '找站很方便', '远程帮助及时有效', '车机交互体验很好', '排队规则非常合理'], 'ev_battery_id': 'P0223981BB00823V218952L18A00092', 'diagnosis_tag': [], 'ev_id': 'e433fc8b69534cf7848b293b7bd540e6', 'ev_type': 'ES6', 'service_battery_id': 'P0223981BG28623V218952L15B00084'}}

		# service_data:  {'PS-NIO-68e3c25b-a862cbbfe433fc8b69534cf7848b293b7bd540e61730734680250': {'ev_battery_original_type': 8, 'ev_battery_real_soc': 12.0, 'ev_battery_soc': 7.0, 'service_start_time': 1730734680250, 'service_battery_original_type': 8, 'service_battery_real_soc': 96.0, 'service_battery_soc': 93.0, 'service_end_time': 1730734893043}}
		with open(f'satisfy_data_{start}_{end}.csv', 'w', newline='') as csvfile:
			writer = csv.writer(csvfile)
			writer.writerow(['服务ID', '评价内容', '评价时间', '设备ID', '是否有效', '订单ID', '设备类型', '低分原因', '评价星级', '解决方案', '评价标签', '诊断标签', '车辆ID', '车辆类型', '车辆电池ID', '旧电池度数', '旧电池SOC', '旧电池SOC', '服务电池ID', '新电池度数', '新电池SOC', '新电池SOC', '服务开始时间', '服务结束时间', '订单开始时间', '订单结束时间', '计费度数', '告警ID', '告警产生时间', '告警消除时间', '告警等级', '告警名称'])
			rows = []

			for service_id, service_info in service_data.items():
				satisfy_info = satisfy_data[service_id]
				order_info = order_data.get(service_id)
				if order_info is None:
					order_info = {}
				_, ev_battery_capacity = self.convert_battery_type(service_info['ev_battery_original_type'])
				_, service_battery_capacity = self.convert_battery_type(service_info['service_battery_original_type'])
				row = [
					service_id,
					satisfy_info["comment"],
					satisfy_info["comment_time"],
					satisfy_info["device_id"],
					satisfy_info['is_valid'],
					satisfy_info['order_id'],
					satisfy_info['project'],
					satisfy_info['reason'],
					satisfy_info['score'],
					satisfy_info['solution'],
					satisfy_info['user_tag'],
					satisfy_info['diagnosis_tag'],
					satisfy_info['ev_id'],
					satisfy_info['ev_type'],
					satisfy_info['ev_battery_id'],
					ev_battery_capacity,
					service_info['ev_battery_real_soc'],
					service_info['ev_battery_soc'],
					satisfy_info['service_battery_id'],
					service_battery_capacity,
					service_info['service_battery_real_soc'],
					service_info['service_battery_soc'],
					service_info['service_start_time'],
					service_info['service_end_time'],
					order_info.get('order_start_time'),
					order_info.get('order_end_time'),
					order_info.get('electricity_kwh'),
				]
				
				# alarms
				has_alarm = False
				if satisfy_info['project'] != "PowerSwap":
					coll_alarm = satisfy_info['project'].lower()
					alarms = backend_mongo.client['alarminfo'][coll_alarm].find({'device_id': satisfy_info["device_id"], 'create_ts': {'$gte': service_info['service_start_time'], '$lt': service_info['service_end_time']}, 'data_id': {'$exists': True}})
					for alarm in alarms:
						has_alarm = True
						new_row = copy.deepcopy(row)
						new_row.extend([
							alarm['data_id'],
							alarm.get('create_ts'),
							alarm.get('clear_ts'),
							alarm_info[alarm['data_id']].get('alarm_level'),
							alarm_info[alarm['data_id']].get('var_cn_name'),
						])
						rows.append(new_row)
				if not has_alarm:
					rows.append(row)
				print(satisfy_info['project'], satisfy_info["device_id"], satisfy_info['order_id'], service_id)
			
			writer.writerows(rows)
		print(f'done satisfy_data_{start}_{end}.csv')

	# 同步生产环境的满意度及相关所有数据到测试环境（环境不通，没用）
	def sync_satisfy_data(self, order_ids):
		backend_mongo = MongoClient(Welkin_CN_PROD_Mongo_URI)
		plc_mongo = MongoClient(Welkin_CN_PROD_PLC_Mongo_URI)
		test_mongo = MongoClient(Welkin_CN_TEST_Mongo_URI)

		for order_id in order_ids:
			satisfy_data = test_mongo.client['serviceinfo']['satisfy_data'].find_one({'order_id': order_id})
			print(satisfy_data)
			# test_mongo.client['serviceinfo']['satisfy_data'].update_one({'order_id': order_id}, {'$set': satisfy_data}, upsert=True)
			# project = satisfy_data['project']
			# service_start_time = satisfy_data['service_start_time']
			# device_id = satisfy_data['device_id']
			# start_time = service_start_time - (3600 * 2) * 1000
			# end_time = service_start_time
			# print(start_time, end_time)

			# coll_project = ''
			# if project == 'PowerSwap2':
			# 	coll_project = 'pus2'
			# elif project == 'PUS3':
			# 	coll_project = 'pus3'
			# elif project == 'PUS4':
			# 	coll_project = 'pus4'
			# cursor = backend_mongo.client['serviceinfo'][coll_project].find_many({'device_id': device_id, 'service_start_time': {'gte': start_time, '$lte': end_time}})
			# for record in cursor:
			# 	test_mongo.client['serviceinfo'][coll_project].update_one({'service_id': record['service_id']}, {'$set': record}, upsert=True)
			# 	order_data = plc_mongo.client['serviceinfo'][f'order_info_full_{project.lower()}'].find_one({'service_id': record['service_id']})
			# 	test_mongo.client['serviceinfo'][f'order_info_full_{project.lower()}'].update_one({'_id': order_id}, {'$set': order_data}, upsert=True)
			# 	alarm_cursor = backend_mongo.client['alarminfo'][project.lower()].find_many({'device_id': device_id, 'create_ts': {'$gte': record['service_start_time'], '$lt': record['service_end_time']}})
			# 	alarms = []
			# 	for alarm in alarm_cursor:
			# 		alarms.append(alarm)
			# 	test_mongo.client['alarminfo'][project.lower()].insert_many(alarms)

	# 检查db中是否所有collection都建了某个索引
	def check_index_in_collections(self, index_name):
		if self.db_name == "":
			print("`db_name` is required!")
			return
		collections = self.client[self.db_name].list_collection_names(session=None)
		for col in collections:
			indexes = self.client[self.db_name][col].index_information()
			if index_name not in indexes:
				print(f"Collection {col} does not have index {index_name}")

	# 对比按月分表和不分表的服务信息
	def compare_service_info(self):
		db1 = 'serviceinfo'
		db2 = 'serviceinfo-pus3'
		col1 = 'pus3'
		col2 = '1_2'
		cursor1 = self.client[db1][col1].find({'service_start_time': {'$gte': 1735833600000, '$lt': 1735884000000}})
		cursor2 = self.client[db2][col2].find({'service_start_time': {'$gte': 1735833600000, '$lt': 1735884000000}})
		service_info1 = {}
		service_info2 = {}
		for item in cursor1:
			service_info1[item['service_id']] = item
		for item in cursor2:
			service_info2[item['service_id']] = item
		for service_id, info in service_info1.items():
			if service_info2.get(service_id) is None:
				print(f'{service_id} not in {db2}.{col2}')
		for service_id, info in service_info2.items():
			if service_info1.get(service_id) is None:
				print(f'{service_id} not in {db1}.{col1}')

	# 统计每日服务次数
	def count_daily_service(self, start, end):
		start_date = datetime.datetime.strptime(start, '%Y-%m-%d')
		end_date = datetime.datetime.strptime(end, '%Y-%m-%d')
		date_list = []
		while start_date <= end_date:
			date_list.append(int(start_date.timestamp() * 1000))
			start_date += datetime.timedelta(days=1)
		db = 'serviceinfo'
		def update_service_daily_count(project, day):
			count = self.client[db][rename_project(project)].count_documents({'service_start_time': {'$gte': day, '$lt': day + 86400000}})
			record = {
				'day': day,
				'project': project,
				'count': count,
				'day_str': datetime.datetime.fromtimestamp(day / 1000).strftime('%Y-%m-%d'),
				'date': datetime.datetime.fromtimestamp(day / 1000)
			}
			self.client[db]['service_daily_count'].update_one({'day': day, 'project': project}, {'$set': record}, upsert=True)
		for day in date_list:
			update_service_daily_count('PowerSwap', day)
			update_service_daily_count('PowerSwap2', day)
			update_service_daily_count('PUS3', day)
			update_service_daily_count('PUS4', day)
			print(datetime.datetime.fromtimestamp(day / 1000).strftime('%Y-%m-%d'))

	def refresh_in_service_alarm(self, project):
		db_alarm = 'alarminfo'
		col1 = project.lower()
		col2 = f'{project.lower()}_1'
		col3 = f'{project.lower()}_12'
		db_service = 'serviceinfo'
		col_service = rename_project(project)
		start_ts = int(datetime.datetime.strptime('2024-12-01 00:00:00', '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
		end_ts = int(datetime.datetime.strptime('2025-01-06 16:00:00', '%Y-%m-%d %H:%M:%S').timestamp() * 1000)
		# 删除告警脏数据
		# self.client[db_alarm][col1].update_many({'create_ts': {'$gte': start_ts, '$lt': end_ts}}, {'$unset': {"in_service": ""}})

		offset = 0
		limit = 100
		while True:
			print(offset)
			service_cursor = self.client[db_service][col_service].find({'service_start_time': {'$gte': start_ts, '$lt': end_ts}}).skip(offset).limit(limit)
			count = 0
			for service in service_cursor:
				if service.get('device_id') is None or service.get('service_start_time') is None or service.get('service_end_time') is None:
					continue
				count += 1
				self.client[db_alarm][col1].update_many({'device_id': service['device_id'], 'create_ts': {'$gte': service['service_start_time'], '$lte': service['service_end_time']}}, {'$set': {"in_service": True}})
				if service['service_end_time'] < 1735660800000:
					self.client[db_alarm][col3].update_many({'device_id': service['device_id'], 'create_ts': {'$gte': service['service_start_time'], '$lte': service['service_end_time']}}, {'$set': {"in_service": True}})
				elif service['service_start_time'] >= 1735660800000:
					self.client[db_alarm][col2].update_many({'device_id': service['device_id'], 'create_ts': {'$gte': service['service_start_time'], '$lte': service['service_end_time']}}, {'$set': {"in_service": True}})
				else:
					self.client[db_alarm][col2].update_many({'device_id': service['device_id'], 'create_ts': {'$gte': service['service_start_time'], '$lte': service['service_end_time']}}, {'$set': {"in_service": True}})
					self.client[db_alarm][col3].update_many({'device_id': service['device_id'], 'create_ts': {'$gte': service['service_start_time'], '$lte': service['service_end_time']}}, {'$set': {"in_service": True}})
			offset += limit
			print(f'service count: {count}')
			if count == 0:
				break

	def find_login_history(self):
		file_path = 'beijing.xlsx'
		output_filename = 'beijing_with_history.xlsx'

		try:
			workbook = openpyxl.load_workbook(file_path)
			sheet = workbook.active
		except FileNotFoundError:
			print(f"Error: File not found at {file_path}")
			return
		except Exception as e:
			print(f"Error reading excel file: {e}")
			return

		header = [cell.value for cell in sheet[1]]
		try:
			device_id_col_index = header.index('device_id') + 1
		except ValueError:
			print("Error: 'device_id' column not found in Excel file.")
			return

		history_col_name = "Login History"
		if history_col_name not in header:
			history_col_index = len(header) + 1
			sheet.cell(row=1, column=history_col_index, value=history_col_name)
		else:
			history_col_index = header.index(history_col_name) + 1

		login_history_collection = self.client['oauth']['login_history']
		
		def format_time_gmt8(ms_timestamp):
			if not ms_timestamp or int(ms_timestamp) == 0:
				return "N/A"
			try:
				ts = int(ms_timestamp) / 1000
				tz_gmt8 = datetime.timezone(datetime.timedelta(hours=8))
				return datetime.datetime.fromtimestamp(ts, tz=tz_gmt8).strftime('%Y-%m-%d %H:%M:%S')
			except (ValueError, TypeError):
				return "Invalid Timestamp"

		for row_num in range(2, sheet.max_row + 1):
			device_id = sheet.cell(row=row_num, column=device_id_col_index).value
			if not device_id:
				continue
			
			print(f"Processing device_id: {device_id}")

			projection = {
				"username": 1,
				"user_id": 1,
				"device_id": 1,
				"login_time": 1,
				"logout_time": 1,
				"_id": 0
			}

			try:
				# Find the single latest record for the device_id
				query = {"device_id": device_id}
				latest_record = login_history_collection.find_one(
					query,
					projection,
					sort=[("login_time", -1)]
				)

				result_string = "No records found"
				if latest_record:
					# Check if the latest record meets the criteria
					login_time = latest_record.get("login_time", 0)
					logout_time = latest_record.get("logout_time") # Can be None or 0
					if login_time > 1751212800000 or logout_time == 0:
						# If criteria met, format it
						formatted_record = {
							"username": latest_record.get("username"),
							"user_id": latest_record.get("user_id"),
							"device_id": latest_record.get("device_id"),
							"login_time": format_time_gmt8(login_time),
							"logout_time": format_time_gmt8(logout_time)
						}
						result_string = json.dumps([formatted_record], ensure_ascii=False, indent=2)

				sheet.cell(row=row_num, column=history_col_index, value=result_string)

			except Exception as e:
				error_message = f"Error processing device_id {device_id}: {e}"
				print(error_message)
				sheet.cell(row=row_num, column=history_col_index, value=error_message)

		try:
			workbook.save(output_filename)
			print(f"Processing complete. Results saved to {output_filename}")
		except Exception as e:
			print(f"Error saving Excel file: {e}")


def rename_project(project):
	if project == 'PowerSwap2':
		return 'pus2'
	elif project == 'PUS3':
		return 'pus3'
	elif project == 'PUS4':
		return 'pus4'
	elif project == 'PowerSwap':
		return 'pus1'
	return project


if __name__ == "__main__":
	mc = MongoClient(Welkin_CN_PROD_Mongo_URI)
	# mc = MongoClient(Welkin_CN_TEST_Mongo_URI)
	# mc = MongoClient(Welkin_CN_PROD_PLC_Mongo_URI)

	# mc.db_name = "plc-record-pus2_7_8"
	# colls = mc.get_plc_record_collections()
	# print(colls)

	# mc.count_daily_service('2024-12-31', '2024-12-31')

	# mc.refresh_in_service_alarm('PowerSwap2')
	# mc.refresh_in_service_alarm('PUS3')
	# mc.refresh_in_service_alarm('PUS4')

	# mc.db_name = 'plc-record-pus2_5_6'
	# mc.get_collection_empty_size()

	mc.oauth()

	# mc.sync_satisfy_data(['825238250034843951'])

	# mc.satisfy_csv('2024-11-04', '2024-11-05')
	# mc.satisfy_csv('2024-11-05', '2024-11-06')
	# mc.satisfy_csv('2024-11-06', '2024-11-07')
	# mc.satisfy_csv('2024-11-07', '2024-11-08')
	# mc.satisfy_csv('2024-11-08', '2024-11-09')
	# mc.satisfy_csv('2024-11-09', '2024-11-10')
	# mc.satisfy_csv('2024-11-10', '2024-11-11')


	# mc.oplog_op_to_csv('2024-10-01', '2024-10-31', 10)
	# mc.oplog_op_to_csv('2024-10-01', '2024-10-31', 60)


	# mc = MongoClient(Welkin_EU_New_PROD_Mongo_URI)
	# mc.db_name = "imageinfo-pus2"
	# mc.list_image_to_csv()
	# mc.db_name = "imageinfo-pus3"
	# mc.list_image_to_csv()

	# mc.db_name = "plc-record-pus3_1_2"
	# mc.delete_outdate_data()

	# mc.sync_stuck_ev_type()
	# mc.refresh_alarm("PUS3")
	# mc.refresh_alarm("PUS4")

	# mc.find_empty_camera('pus2')
	# mc.find_empty_camera('pus3')
	# mc.find_empty_camera('pus4')

	# mc.db_name = "camera_management"
	# mc.update_camera_data('pus2')
	# mc.update_camera_data('pus3')
	# mc.update_camera_data('pus4')
	
	# mc.db_name = "device2cloud"
	# mc.col_name = "oss-realtime-pus3"
	# mc.import_data_from_json_file_2("./oss-realtime-pus3.json")
	# mc.update_alarm_basic_info()
	# mc.drop_realtime2_column()
	# mc.db_name = "factoryData"
	# mc.col_name = "torque-report"
	# mc.get_torque_report("PS-NIO-b3ceae3c-4966b17e")
	# mc.get_torque_serviceinfo("PS-NIO-92c3a055-30ae1ee6", "10cc275b6392c82d411034d9034e2ac9")
	
	# mc.delete_invalid_oss_data_id()

	# mc.find_login_history()
	# mc2 = MongoClient(Welkin_CN_PROD_PLC_Mongo_URI)
	# mc2.get_all_collections("plc-record-pus2_5_6")



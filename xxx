charge_strategy string, day string, device_id string,
90_swapping_queuetime double,
90_swapping_queuetime_battery double,
90_swapping_queuetime_user double, MODEL_VERSION string, Off_peak_income double, avg_battery_chargingtime double, avg_power double, avg_swapping_queuetime double, avg_swapping_queuetime_battery double, avg_swapping_queuetime_user double, battery_electricity_consumption double, battery_electricity_cost double, capacity_factor double, cost_kwh double, device_electricity_consumption double, device_info string, energy_efficiency double, err_code double, hourly_avg_swapping_queuetime array<double>, hourly_battery_charging_nums array<double>, hourly_battery_chargingtime array<double>, hourly_battery_electricity_cost array<double>, hourly_capacity_factor array<double>, hourly_load_factor array<double>, hourly_module_utilization_ratio array<double>, hourly_swapping_service_nums array<double>, income_kwh double, load_factor double, marginal_contribution_margin double, max_power double, message string, model_trigger_time string, module_electricity_consumption double, module_nums double, module_utilization_ratio double, non_charging_electricity_consumption double, non_charging_electricity_cost double, profit_kwh double, project string, rated_power double, remain_electricity_consumption double, scenario_info string, sct_electricity_consumption double, sct_service_nums double, sct_serviced_nums double, avg_sct_queuetime double,
90_avg_sct_queuetime double, hourly_sct_service_nums array<double>, hourly_avg_sct_queuetime array<double>, service_info string, simulation_id string, simulation_info string, single_module_actual_work_time double, single_module_power_limit double, single_module_rated_work_time double, swap_e_consumption double, swap_service_income double, swapped_service_nums double, swapping_service_nums double, task_id string, total_cost double, total_income double
import pandas as pd

# 定义输入和输出文件名
input_filename = 'realtime/fire_compartment_extended_pus4.csv'
output_filename = 'realtime/fire_compartment_deduplicated_pus4.csv'

# 读取CSV文件
try:
    df = pd.read_csv(input_filename)

    # 基于 'device_id' 列去重，保留第一次出现的记录
    deduplicated_df = df.drop_duplicates(subset=['device_id'], keep='first')

    # 将去重后的数据写入新的CSV文件
    deduplicated_df.to_csv(output_filename, index=False)

    print(f"成功处理文件。去重后的数据已保存到 '{output_filename}'")
    print(f"原始行数: {len(df)}")
    print(f"去重后行数: {len(deduplicated_df)}")

except FileNotFoundError:
    print(f"错误: 输入文件 '{input_filename}' 未找到。")
except Exception as e:
    print(f"处理文件时发生错误: {e}")
import pandas as pd
import os

# 定义文件路径
file1_path = 'servers/1.xlsx'
file2_path = 'servers/虚拟机.xlsx'
output_path = 'merged_servers.xlsx'

# 检查文件是否存在
if not os.path.exists(file1_path):
    print(f"错误：文件 '{file1_path}' 不存在。")
if not os.path.exists(file2_path):
    print(f"错误：文件 '{file2_path}' 不存在。")

try:
    # 读取两个Excel文件
    df1 = pd.read_excel(file1_path, sheet_name='待处理的机器')
    df2 = pd.read_excel(file2_path, sheet_name='数据表')

    # 从df1中选择需要的列
    columns_to_merge = ['主机名', '迁移状态', '状态', '业务标签', '备注']
    df1_selected = df1[columns_to_merge]

    # 基于'主机名'列合并两个DataFrame
    # 使用左连接，以df2为主表
    merged_df = pd.merge(df2, df1_selected, on='主机名', how='left')

    # 将合并后的数据保存到新的Excel文件
    merged_df.to_excel(output_path, index=False)

    print(f"合并后的文件已保存为 '{output_path}'")

except Exception as e:
    print(f"处理文件时发生错误: {e}")
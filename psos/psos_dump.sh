#!/bin/bash

# 检查是否提供了命令行参数
if [ -z "$1" ]; then
  echo "Error: Start date is required. Usage: $0 YYYY-MM-DD"
  exit 1
fi

# 使用传入的参数作为 START_DATE
START_DATE="$1"

echo "Start date: $START_DATE"

# 将日期转换为秒数（自 Unix 时间戳）
START_DATE_SEC=$(date -d "$START_DATE 00:00:00" +%s)

START_TS=$((START_DATE_SEC * 1000))  # 转换为毫秒
NEXT_DATE_SEC=$((START_DATE_SEC + 86400))  # 加一天
END_TS=$((NEXT_DATE_SEC * 1000))  # 转换为毫秒


# 创建 JSON payload 并发送请求
PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PowerSwap2", charge_strategy: "eps_silent", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PowerSwap2", charge_strategy: "only_eps", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PowerSwap2", charge_strategy: "non_stra", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS3", charge_strategy: "eps_silent", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS3", charge_strategy: "only_eps", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS3", charge_strategy: "non_stra", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS4", charge_strategy: "eps_silent", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS4", charge_strategy: "only_eps", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS4", charge_strategy: "non_stra", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"
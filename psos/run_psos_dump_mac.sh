#!/bin/bash

# 定义起始日期和结束日期
START_DATE="2025-03-26"
END_DATE="2025-01-01"

# 将日期转换为秒数（自 Unix 时间戳）
current_date_sec=$(date -j -f "%Y-%m-%d" "$START_DATE" +"%s")
end_date_sec=$(date -j -f "%Y-%m-%d" "$END_DATE" +"%s")

# 循环调用 ./psos_dump.sh 脚本
while [ "$current_date_sec" -ge "$end_date_sec" ]; do
  # 将当前秒数转换回日期格式
  current_date=$(date -j -f "%s" "$current_date_sec" +"%Y-%m-%d")
  
  # 调用 ./psos_dump.sh 脚本并传入当前日期
  echo "Calling ./psos_dump.sh with date: $current_date"
#   ./psos_dump.sh "$current_date"
  
#   # 检查脚本是否成功执行
#   if [ $? -eq 0 ]; then
#     echo "Successfully executed for date: $current_date"
#   else
#     echo "Failed to execute for date: $current_date"
#     exit 1
#   fi

#   # 等待 2 小时（7200 秒）
#   echo "Waiting for 2 hours before the next execution..."
#   sleep 7200

  # 日期递减一天（86400 秒）
  current_date_sec=$((current_date_sec - 86400))
done

echo "All executions completed."
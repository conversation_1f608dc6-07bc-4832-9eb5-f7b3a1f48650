#!/bin/bash
# 设置环境变量以支持UTF-8，避免乱码
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# 定义API的URL
URL="https://api-welkin-backend.nioint.com/algorithm/v1/psos/config/batch"

# 定义配置名称前缀
CONFIG_NAME_PREFIX="serein.chen0804_05"

# 定义批次大小
BATCH_SIZE=30

# 定义开始和结束日期，两个日期均包含
START_DATE="2025-05-01"
END_DATE="2025-05-05"

# 定义项目及其对应的设备文件
# 格式: "PROJECT_NAME:DEVICE_FILE_PATH"
PROJECTS_CONFIG=(
    "PowerSwap2:psos_devices_ps2.txt"
    "PUS3:psos_devices_pus3.txt"
    "PUS4:psos_devices_pus4.txt"
)

# --- 辅助函数 ---

# 将 YYYY-MM-DD 格式的日期转换为纪元秒
# $1: 日期字符串 (例如 "2025-06-15")
date_to_seconds() {
    local date_str="$1"
    if [[ "$(uname)" == "Darwin" ]]; then
        # macOS (BSD) date
        date -j -f "%Y-%m-%d %H:%M:%S" "$date_str 00:00:00" +"%s"
    else
        # Linux (GNU) date
        date -d "$date_str" +"%s"
    fi
}

# 将纪元秒转换为指定格式的日期字符串
# $1: 纪元秒
# $2: 格式字符串 (例如 "%Y-%m-%d")
seconds_to_date() {
    local seconds="$1"
    local format="$2"
    if [[ "$(uname)" == "Darwin" ]]; then
        # macOS (BSD) date
        date -j -f "%s" "$seconds" +"$format"
    else
        # Linux (GNU) date
        date -d "@$seconds" +"$format"
    fi
}

# --- 主处理循环 ---

# 将日期转换为自纪元以来的秒数
START_DATE_SEC=$(date_to_seconds "$START_DATE")
END_DATE_SEC=$(date_to_seconds "$END_DATE")

# 遍历所有项目配置
for config in "${PROJECTS_CONFIG[@]}"; do
    # 解析项目名称和设备文件路径
    PROJECT="${config%%:*}"
    DEVICE_FILE="${config#*:}"

    echo "=================================================="
    echo "开始处理项目: $PROJECT"
    echo "=================================================="

    # --- 预计算与检查 ---
    if [ ! -f "$DEVICE_FILE" ]; then
        echo "警告: 设备文件 '$DEVICE_FILE' 未找到，跳过项目 $PROJECT"
        continue
    fi

    # 将所有设备ID读入一个数组 (增强版，处理无尾随换行符的文件)
    ALL_DEVICES=()
    while IFS= read -r line || [[ -n "$line" ]]; do
        ALL_DEVICES+=("$line")
    done < "$DEVICE_FILE"
    TOTAL_DEVICES=${#ALL_DEVICES[@]}

    if [ $TOTAL_DEVICES -eq 0 ]; then
        echo "警告: 设备文件 '$DEVICE_FILE' 为空，跳过项目 $PROJECT"
        continue
    fi

    echo "从 '$DEVICE_FILE' 文件中读取到总共 $TOTAL_DEVICES 个设备。"

    # 分批处理设备
    for (( i=0; i<TOTAL_DEVICES; i+=BATCH_SIZE )); do
        # 获取当前批次的设备ID切片
        DEVICES=("${ALL_DEVICES[@]:i:BATCH_SIZE}")
        CURRENT_BATCH_NUM=$((i/BATCH_SIZE + 1))
        
        echo "---"
        echo "正在处理批次 $CURRENT_BATCH_NUM 包含 ${#DEVICES[@]} 个设备..."

        # 针对当前批次，遍历日期范围
        CURRENT_DATE_SEC=$START_DATE_SEC
        while [ "$CURRENT_DATE_SEC" -le "$END_DATE_SEC" ]; do
            CURRENT_DATE_STR=$(seconds_to_date "$CURRENT_DATE_SEC" "%Y-%m-%d")
            
            # 计算当天的开始和结束时间戳 (毫秒)
            START_TS=${CURRENT_DATE_SEC}000
            NEXT_DATE_SEC=$(($CURRENT_DATE_SEC + 86400))
            END_TS=${NEXT_DATE_SEC}000

            # 使用当前日期和批次号格式化config_name
            CONFIG_NAME="${CONFIG_NAME_PREFIX}$(seconds_to_date "$CURRENT_DATE_SEC" "%m%d")_batch_${CURRENT_BATCH_NUM}"

            # 高效地构建JSON中的设备列表字符串
            DEVICE_LIST_JSON=$(printf '"%s",' "${DEVICES[@]}")
            DEVICE_LIST_JSON=${DEVICE_LIST_JSON%,} # 移除末尾的逗号

            # 创建JSON载荷
            PAYLOAD=$(cat <<EOF
{
    "config_name": "$CONFIG_NAME",
    "remark": "",
    "project": "$PROJECT",
    "start_ts": $START_TS,
    "end_ts": $END_TS,
    "devices": [
        $DEVICE_LIST_JSON
    ]
}
EOF
)

            # 使用curl发送POST请求
            echo "正在为日期 $CURRENT_DATE_STR, 批次 $CURRENT_BATCH_NUM 发送请求..."
            curl -X POST -H "Content-Type: application/json" -H "X-User-ID: caca.wang" -d "$PAYLOAD" "$URL"
            echo "  - START_TS: $START_TS, END_TS: $END_TS"
            echo # 输出一个换行符以提高可读性
            # echo "$PAYLOAD" # 如果需要调试，可以取消此行的注释

            # 移至下一天
            CURRENT_DATE_SEC=$NEXT_DATE_SEC
            
            # 在同一批次的下一个日期之前休眠
            if [ "$CURRENT_DATE_SEC" -le "$END_DATE_SEC" ]; then
                echo "休眠1秒..."
                sleep 1
            fi
        done
        
        # 如果还有更多批次要处理，则在批次之间休眠
        if (( i + BATCH_SIZE < TOTAL_DEVICES )); then
            echo "批次 $CURRENT_BATCH_NUM 处理完毕。在处理下一批次前休眠1秒..."
            sleep 1
        fi
    done
    echo "项目 $PROJECT 处理完毕。"
done

echo "=================================================="
echo "所有项目处理完毕。脚本执行结束。"

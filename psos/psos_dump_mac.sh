#!/bin/bash

# 检查是否提供了命令行参数
if [ -z "$1" ]; then
  echo "Error: Start date is required. Usage: $0 YYYY-MM-DD"
  exit 1
fi

# 使用传入的参数作为 START_DATE
START_DATE="$1"

echo "Start date: $START_DATE"

# Convert dates to seconds since epoch with hours, minutes, and seconds set to 0
START_DATE_SEC=$(date -j -f "%Y-%m-%d %H:%M:%S" "$START_DATE 00:00:00" +"%s")

START_TS=$(date -j -f "%s" "$START_DATE_SEC" +"%s000")
NEXT_DATE_SEC=$(($START_DATE_SEC + 86400))
END_TS=$(date -j -f "%s" "$NEXT_DATE_SEC" +"%s000")


# Create the JSON payload
PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PowerSwap2", charge_strategy: "eps_silent", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PowerSwap2", charge_strategy: "only_eps", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PowerSwap2", charge_strategy: "non_stra", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"


PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS3", charge_strategy: "eps_silent", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS3", charge_strategy: "only_eps", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS3", charge_strategy: "non_stra", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"


PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS4", charge_strategy: "eps_silent", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS4", charge_strategy: "only_eps", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

PAYLOAD=$(jq -n --argjson start_ts "$START_TS" --argjson end_ts "$END_TS" '{project: "PUS4", charge_strategy: "non_stra", start_ts: $start_ts, end_ts: $end_ts, from_checkpoint: true}')
curl -X POST -H "Content-Type: application/json" -d "$PAYLOAD" "https://api-welkin-backend.nioint.com/algorithm/v1/psos/task/run"

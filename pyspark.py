

from pymongo import MongoClient
from pyspark.sql import SparkSession
import sys
import requests
import json
from datetime import datetime, timedelta


spark = SparkSession.builder.appName("Hive to MongoDB"). \
    config("spark.sql.adaptive.enabled", "true").enableHiveSupport(). \
    config("spark.jars",
           "hdfs:///user/enguang.zhou/pyspark/mongo-spark-connector_2.12-10.1.1-all.jar,hdfs:///user/enguang.zhou/pyspark/mongo-java-driver-3.9.0.jar"). \
    config("spark.mongodb.write.connection.uri",
           "mongodb://mongouser:#^GThW#YdrNZVrZ)@10.130.104.66:27017,10.130.104.30:27017/algorithm.alarm_test"). \
    config("spark.mongodb.read.connection.uri",
           "mongodb://mongouser:#^GThW#YdrNZVrZ)@10.130.104.66:27017,10.130.104.30:27017/algorithm.alarm_test"). \
    getOrCreate()

# hive表里用的设备id是虚拟id，平台侧使用的全是真实id，这里需要映射一下
resource_id_to_device_id = {}
response = requests.get('http://api-welkin-backend.nioint.com/device/v1/devices/list_all')
if response.status_code == 200 :
    print("http 请求获取全部设备成功")
else:
    print('请求失败:', response.status_code)
    raise SystemExit

resp_dic = json.loads(response.text)
if resp_dic['err_code'] != 0:
    print('请求失败:',resp_dic)
    raise SystemExit

for device in resp_dic['data']:
    if device['resource_id'] != '':
        resource_id_to_device_id[device['resource_id']] = device['device_id']

# # 获取当前时间
# current_time = datetime.now()
# # 分区时间
# partition_time = current_time - timedelta(days=1)
# partition_time_str = partition_time.strftime("%Y%m%d")
# # 减去2天
# start_time = current_time - timedelta(days=2)
# # 格式化当前日期时间
# formatted_time = start_time.strftime("%Y-%m-%d")
# start_date = formatted_time

# print("################################")
# print(f'分区时间:{partition_time_str} start_date:{start_date}')
# print("################################")

print(f'##########################开始处理繁忙度数据')
sql = f"""
select alarm_type_id,alarm_description,device_id,alarm_raise_time,alarm_clear_time,alarm_state,alarm_level from dm_data_operation.d_f_oss_alarm_info_wide_table where alarm_device_type = 'PowerSwap' and alarm_raise_time > '2024-05-31 00:00:00' and alarm_raise_time < '2024-06-04 00:00:00';
"""
print(sql)

order_busy_rates = spark.sql(sql)

records = []

def prepare_records(row):
    device_id = row.device_id
    if row.device_id in resource_id_to_device_id:
        device_id = resource_id_to_device_id[row.device_id]
    
    # 插入数据到集合中
    document = {
        "alarm_id": row.alarm_type_id,
        "alarm_description": row.alarm_description,
        "device_id": device_id,
        "alarm_raise_time": row.alarm_raise_time,
        "alarm_clear_time": row.alarm_clear_time,
        "alarm_state": row.alarm_state,
        "alarm_level": row.alarm_level
    }
    records.append(document)

# def insert_into_mongodb(row):
#     # 连接到 MongoDB
#     client = MongoClient(
#         "*************************************************************************************************************************"
#     )
#     db = client["algorithm"]
#     collection = db["alarm_test"]

#     # formatted_string = row.service_day.strftime("%Y-%m-%d")

#     device_id = row.device_id
#     if row.device_id in resource_id_to_device_id:
#         device_id = resource_id_to_device_id[row.device_id]
    

#     # 插入数据到集合中
#     document = {
#         "alarm_id": row.alarm_type_id,
#         "alarm_description": row.alarm_description,
#         "device_id": device_id,
#         "alarm_raise_time": row.alarm_raise_time,
#         "alarm_clear_time": row.alarm_clear_time,
#         "alarm_state": row.alarm_state,
#         "alarm_level": row.alarm_level
#     }
#     collection.insert_one(document)
#     # collection.insert_one(document)

def insert_into_mongodb():
    # 连接到 MongoDB
    client = MongoClient(
        "*************************************************************************************************************************"
    )
    db = client["algorithm"]
    collection = db["alarm_test"]

    # formatted_string = row.service_day.strftime("%Y-%m-%d")

    collection.insert_many(records)
    # collection.insert_one(document)


rdd = order_busy_rates.rdd
print("总数: ", rdd.count())
# print(rdd)
rdd.foreach(prepare_records)
print(f'records len: {len(records)}')

import re

def extract_and_write_device_ids(source_file, destination_file):
    """
    从源JSON文件中提取所有device_id，并将其逐行写入目标文本文件。
    """
    try:
        with open(source_file, 'r', encoding='utf-8') as f_source:
            content = f_source.read()
        
        # 使用正则表达式查找所有的 device_id
        # "device_id":"PUS-NIO-579d11b2-ea8b58bd"
        pattern = re.compile(r'"device_id":"([^"]+)"')
        device_ids = pattern.findall(content)
        
        if not device_ids:
            print(f"警告: 在 {source_file} 中没有找到任何 device_id。")
            return

        with open(destination_file, 'w', encoding='utf-8') as f_dest:
            for device_id in device_ids:
                f_dest.write(device_id + '\n')
        
        print(f"成功从 {source_file} 提取了 {len(device_ids)} 个设备ID，并写入到 {destination_file}")

    except FileNotFoundError:
        print(f"错误: 源文件 {source_file} 未找到。")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    extract_and_write_device_ids('device_basic_info.json', 'psos_devices_pus4.txt')
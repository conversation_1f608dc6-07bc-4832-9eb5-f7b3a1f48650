#!/bin/bash
TOTAL_DURATION=$((12 * 60 * 60))
START_TIME=$(date +%s)

while true; do
  # 获取当前时间
  CURRENT_TIME=$(date +%s)
  
  # 计算已经执行的时间
  ELAPSED_TIME=$((CURRENT_TIME - START_TIME))

  echo $CURRENT_TIME
  echo $ELAPSED_TIME
  
  # 检查是否已经执行了12小时
  if [ "$ELAPSED_TIME" -ge "$TOTAL_DURATION" ]; then
    echo "Total duration of 12 hours reached. Exiting..."
    break
  fi

  # 调用接口
  curl --request POST \
    -H "Content-Type:application/json" \
    --url https://api-welkin-backend-stg.nioint.com/device/v1/log-info/PowerThor/NPC-NIO-06767401-f5e799d7/oss/command \
    --header 'X-User-ID: william.shen2' \
    --data '{
    "configuration": 
    {
        "key": "uploadLogFile", 
        "value": "/warehouse/log/ps/2024-09-30 17-05-28.42.log"
    }
    }
    '

  # 等待3分钟（180秒）
  sleep 180
done



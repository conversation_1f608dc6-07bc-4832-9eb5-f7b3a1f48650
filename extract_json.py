import json

def get_resource_ids(json_file_path):
    """
    读取 JSON 文件，提取每个元素的 'resource_id'，并以指定格式打印。

    Args:
        json_file_path (str): JSON 文件的路径。
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if not isinstance(data, list):
            print("Error: The JSON file does not contain a JSON array.")
            return

        resource_ids = []
        for item in data:
            if isinstance(item, dict) and 'resource_id' in item:
                resource_ids.append(f'"{item["resource_id"]}"')
            else:
                print(f"Warning: Skipping item due to missing or invalid 'resource_id': {item}")

        if resource_ids:
            print(f"[{','.join(resource_ids)}]")
        else:
            print("No resource_ids found in the JSON file.")

    except FileNotFoundError:
        print(f"Error: File not found at {json_file_path}")
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON format in {json_file_path}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

# 示例用法：
# 假设你的 JSON 文件名为 'your_file.json' 并且内容如下：
# [
#     {"name": "Item 1", "resource_id": "NPC-NIO-a7cc37df-e42288d2"},
#     {"name": "Item 2", "resource_id": "NPC-NIO-b8dd48e3-f53399e3"},
#     {"name": "Item 3", "description": "Some text"}
# ]

# 调用函数并传入你的 JSON 文件路径
get_resource_ids('/Users/<USER>/Downloads/psc4.json')
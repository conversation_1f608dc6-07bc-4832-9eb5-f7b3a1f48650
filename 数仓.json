cfg: {
    "httpServer": {
        "listenAddr": "0.0.0.0:8080",
        "graceShutdownPeriod": "10s"
    },
    "log": {
        "dirPath": "/tmp/logs",
        "maxSize": 200,
        "remainDays": 30,
        "maxBackups": 10
    },
    "sentry": {
        "appId": "101021",
        "appSecret": "02b13dfb01e19a34a6266b39d32e2f15"
    },
    "welkin": {
        "frontendUrl": "https://pp-welkin-stg.nioint.com",
        "backendUrl": "https://api-welkin-backend-stg.nioint.com",
        "backendStgUrl": "https://api-welkin-backend-stg.nioint.com",
        "redRabbitUrl": "https://api-redrabbit-test.nioint.com",
        "pangeaUrl": "https://pangea-test.nioint.com",
        "gateUrl": "https://welkin-gate-stg.nioext.com",
        "psosUrl": "https://api-welkin-algorithm-psos-stg.nioint.com",
        "algorithmUrl": "https://api-welkin-algorithm-stg.nioint.com",
        "tsp": {
            "url": "https://tsp-stg.nioint.com",
            "alpsUrl": "https://tsp-alps-stg.nioint.com",
            "filterFields": "package_part_number,battery_id,lv_batt_soc"
        },
        "ehrUrl": "https://ehr-common-service-stg.nioint.com",
        "peopleUrl": "https://napoleon-stg.nioint.com",
        "aesKey": "YlZxe21giEC+z0daPW1PGxHc4g4Y0haK",
        "algorithm": {
            "PowerSwap2": {
                "cmsEffectivePeriod": [],
                "theoreticalFtt": 0.98,
                "expectFttSuccessRate": 0.96,
                "ttcSwitch": false,
                "uploadDataLimit": 300,
                "cmsOrderPredictVersion": 0
            },
            "PUS3": {
                "cmsEffectivePeriod": [],
                "theoreticalFtt": 0.98,
                "expectFttSuccessRate": 0,
                "ttcSwitch": true,
                "uploadDataLimit": 300,
                "cmsOrderPredictVersion": 0
            },
            "PUS4": {
                "cmsEffectivePeriod": null,
                "theoreticalFtt": 0,
                "expectFttSuccessRate": 0,
                "ttcSwitch": false,
                "uploadDataLimit": 300,
                "cmsOrderPredictVersion": 0
            }
        }
    },
    "gitLab": {
        "userName": "jenkins.ppd.s",
        "password": "2bSpRy3CfmgX1kBuTcKe",
        "repo": {
            "proto_buf_ps": "https://git.nevint.com/PERD/swap2.0-protocol-datamodels/proto_buf_ps.git"
        }
    },
    "oss": {
        "powUrl": "https://pow-stg.nioint.com",
        "primeUrl": "https://pow-stg.nioint.com/pe/prime/platform",
        "nmpUrl": "https://pe-nmp-welkin-stg.nioint.com",
        "aiUrl": "http://pe-ai-gateway-stg.nioint.com",
        "powOssUrl": "https://pow-oss-stg.nioint.com"
    },
    "sso": {
        "signUrl": "https://signin-stg.nioint.com",
        "lankaUrl": "https://lanka-stg.nioint.com",
        "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCawK1hnqiYFuICJnBePGrVnohHK0AkscQ3UWlBZp8TjFKFDdf2ciWjKsNHHUFLBBxyYa0EgPnoXJVLjNJz7SUEwMM9kCKtnPizzlhhuFZ1+cuT7CDO7E//UwGfubnpvYi1bWi8Nh1zEb6L1QUD/3ZnwDjkijB7scoQNVYQL2KsCwIDAQAB"
    },
    "fms": {
        "clientId": "welkin_cn_319699",
        "clientSecret": "stg_332b2d24e241603b42cf52195de062ea",
        "priBucketKey": "welkin_cn_pri_daecec",
        "pubBucketKey": "welkin_cn_pub_9be818",
        "url": "https://fms-stg.nioint.com"
    },
    "mongodb": {
        "backend": {
            "uri": "mongodb://mongouser:mwu#5NVBjyCWZJd#@************:27017,*************:27017/test?authSource=admin",
            "ca": "",
            "maxPoolSize": 300
        },
        "backend_support_timeseries": {
            "uri": "mongodb://mongouser:mwu#5NVBjyCWZJd#@************:27017,*************:27017/test?authSource=admin",
            "ca": "",
            "maxPoolSize": 300
        },
        "plc": {
            "uri": "mongodb://mongouser:mwu#5NVBjyCWZJd#@************:27017,*************:27017/test?authSource=admin",
            "ca": "",
            "maxPoolSize": 300
        },
        "rb": {
            "uri": "mongodb://mongouser:EZC!Ak*Ji5hm@*************:27017/admin",
            "ca": "",
            "maxPoolSize": null
        }
    },
    "tdengine": {
        "protocol": "http",
        "host": "welkin-tdengine-test.nioint.com",
        "port": 6041,
        "username": "welkin",
        "password": "x0Fwwqc5x^EizE4X"
    },
    "redis": {
        "type": "standalone",
        "maxIdle": 0,
        "maxActive": 0,
        "idleTimeout": 0,
        "connectTimeout": 0,
        "readTimeout": 0,
        "writeTimeout": 0,
        "uris": [
            "*************:6379"
        ],
        "password": "shgdwyx703hjswu",
        "db": 10
    },
    "minio": {
        "url": "http://api-pp-welkin-minio-test.nioint.com:9000",
        "key": "YBkUC7",
        "secret": "2ltu62W0C1uy",
        "ssl": false
    },
    "feishu": {
        "grantType": "authorization_code",
        "apis": "https://open.feishu.cn/open-apis",
        "scanAppId": "cli_a3b80beabb309013",
        "scanAppSecret": "aJsiCobeiCVXsdt54h5ric5lQpPyoGJ4",
        "polarisAppId": "cli_a4938148fdf8900c",
        "polarisAppSecret": "lOOIz15FX8UfuDwX5EJvkbdn22uy2T5t",
        "appletAppId": "cli_a41d0e8bb97bd00e",
        "appletAppSecret": "3SXaZKgHQgOqZeLsmyIoIfqJ7lQPLmZG",
        "pangeaAppId": "",
        "pangeaAppSecret": ""
    },
    "workflow": {
        "url": "https://rocket-stg.nioint.com",
        "logExportFlowCode": "c79a34b1-e7bc-402c-b7b2-99d90004f733",
        "deviceLoginFlowCode": "ed91c19c-5da1-4ab2-9d8d-49f2b4c194ae",
        "browndragonUserFlowCode": "37678e39-ad5b-4d05-989c-ad4c32d0b5a1",
        "browndragonDeviceFlowCode": "f64e63fd-fe42-497f-9f3a-f79ada18144f",
        "userRoleUpgrade": "525c6189-9317-41d2-a78c-d6d92c0528b8"
    },
    "kafka": {
        "welkin": {
            "broker": "pp-welkin-kafka-test.nioint.com:9092",
            "brokerAddressFamily": "v4",
            "sessionTimeout": 6000,
            "idempotence": false,
            "autoOffsetReset": "latest",
            "autoOffsetStore": true,
            "autoCommit": true,
            "securityProtocol": "sasl_plaintext",
            "saslMechanism": "PLAIN",
            "saslUsername": "welkin",
            "saslPassword": "3FGoagZUxF38vWc2",
            "producers": {
                "topics": {
                    "PUS": {
                        "algorithmData": "algorithm-daily-report",
                        "converter": "converter",
                        "opLog": "op-log",
                        "plcRecord": "plc-record",
                        "sensor": "sensor"
                    },
                    "PUS3": {
                        "algorithmData": "algorithm-daily-report",
                        "converter": "converter-pus3",
                        "di": "di-pus3",
                        "plcRecord": "plc-record-pus3",
                        "sensor": "sensor-pus3",
                        "ttrans": "tank-transfer-record-pus3"
                    }
                }
            },
            "plcConsumers": [
                {
                    "topics": [
                        "plc-record",
                        "plc-record-pus3",
                        "plc-record-pus4",
                        "plc-record-fypus1"
                    ],
                    "groupId": "welkin-plc-record-stg"
                },
                {
                    "topics": [
                        "sensor",
                        "sensor-pus3",
                        "sensor-pus4",
                        "sensor-fypus1"
                    ],
                    "groupId": "welkin-sensor-stg"
                },
                {
                    "topics": [
                        "converter",
                        "converter-pus3",
                        "converter-pus4",
                        "converter-fypus1"
                    ],
                    "groupId": "welkin-converter-stg"
                },
                {
                    "topics": [
                        "op-log"
                    ],
                    "groupId": "welkin-op-log-stg"
                }
            ],
            "welkinConsumers": [
                {
                    "topics": [
                        "di-pus3",
                        "di-pus4"
                    ],
                    "groupId": "welkin-di-stg"
                },
                {
                    "topics": [
                        "tank-transfer-record-pus3",
                        "tank-transfer-record-pus4"
                    ],
                    "groupId": "welkin-tank-transfer-record-stg"
                },
                {
                    "topics": [
                        "algorithm-daily-report"
                    ],
                    "groupId": "algorithm-daily-report-stg"
                },
                {
                    "topics": [
                        "oss2welkin_realtime"
                    ],
                    "groupId": "oss2welkin_realtime-stg"
                }
            ]
        },
        "oss": {
            "broker": "pe-kafka-01-stg.nioint.com:9092,pe-kafka-02-stg.nioint.com:9092,pe-kafka-03-stg.nioint.com:9092",
            "brokerAddressFamily": "v4",
            "sessionTimeout": 6000,
            "idempotence": false,
            "autoOffsetReset": "latest",
            "autoOffsetStore": true,
            "autoCommit": true,
            "securityProtocol": "sasl_plaintext",
            "saslMechanism": "PLAIN",
            "saslUsername": "kmbmanXC",
            "saslPassword": "ZsbEYEDwGjPNwmKG",
            "producers": {
                "topics": [
                    "staging-20001-data_report",
                    "PE-staging-power-device_event"
                ]
            },
            "welkinConsumers": [
                {
                    "topics": [
                        "PEOSS-staging-oss-powerswap_realtime_info_update"
                    ],
                    "groupId": "welkin-service-info-update"
                },
                {
                    "topics": [
                        "PEOSS-staging-oss-resource_info_update"
                    ],
                    "groupId": "welkin-resource-info-update"
                },
                {
                    "topics": [
                        "PE-staging-power-device_customized_welkin"
                    ],
                    "groupId": "power-device_customized_welkin"
                },
                {
                    "topics": [
                        "PE-staging-power-device_customized_30001_events"
                    ],
                    "groupId": "welkin-power-device_customized_30001_events"
                },
                {
                    "topics": [
                        "PE-staging-power-device_customized_30002_events"
                    ],
                    "groupId": "welkin-power-device_customized_30002_events"
                },
                {
                    "topics": [
                        "PE-staging-chargingpile_customized_events"
                    ],
                    "groupId": "welkin-chargingpile_customized_events"
                }
            ],
            "oss2WelkinConsumers": [
                {
                    "topics": [
                        "staging-20001-data_report"
                    ],
                    "groupId": "welkin-20001-data_report"
                },
                {
                    "topics": [
                        "PE-staging-power-device_customized_30001_realtime"
                    ],
                    "groupId": "welkin-power-device_customized_30001_realtime"
                },
                {
                    "topics": [
                        "PE-staging-power-device_customized_30001_alarm"
                    ],
                    "groupId": "welkin-power-device_customized_30001_alarm"
                },
                {
                    "topics": [
                        "PE-staging-power-device_realtime"
                    ],
                    "groupId": "welkin-power-device_realtime"
                },
                {
                    "topics": [
                        "PE-staging-power-device_basic"
                    ],
                    "groupId": "welkin-power-device_basic"
                },
                {
                    "topics": [
                        "PE-staging-power-device_event"
                    ],
                    "groupId": "welkin-power-device_event"
                },
                {
                    "topics": [
                        "PE-staging-power-device_alarm"
                    ],
                    "groupId": "welkin-power-device_alarm"
                },
                {
                    "topics": [
                        "PE-staging-prime_ability_operate_result_info"
                    ],
                    "groupId": "welkin-prime_ability_operate_result_info"
                },
                {
                    "topics": [
                        "PE-staging-prime-alarm_info"
                    ],
                    "groupId": "welkin-prime-alarm_info"
                },
                {
                    "topics": [
                        "PE-staging-edm-realtime-info_items"
                    ],
                    "groupId": "welkin-edm-realtime-info_items"
                }
            ],
            "websiteConsumers": [
                {
                    "topics": [
                        "PE-staging-prime_ability_operate_result_info"
                    ],
                    "groupId": "welkin-oauth-stg"
                }
            ]
        }
    },
    "mems": {
        "url": "https://extra-mems-analyse-test.nio.com"
    },
    "ppdCommonAuth": {
        "domain": "ppd-common-auth-stg.nioint.com",
        "cookie": {
            "name": "ppd-common-auth-session",
            "maxAge": 7200,
            "path": "/",
            "domain": "welkin-gate-stg.nioext.com",
            "secure": false,
            "httpOnly": true
        }
    },
    "cardBot": {
        "appId": "cli_a496bbd1f1fad00e",
        "appSecret": "59H70qO067fHfRtHNdbmA8upnSKM2GyX",
        "encryptKey": "UsRpEpb3pgmyB5zhffmouhMpLsuBPfrX",
        "verificationToken": "kwarRh2AOH15bkVppOaF7emcVRVkQ6In",
        "receivers": {
            "activityStats": [
                "<EMAIL>"
            ],
            "fcrdAlert": [
                "<EMAIL>"
            ],
            "opAlgorithm": [
                "<EMAIL>"
            ]
        }
    },
    "wikiBot": {
        "appId": "",
        "appSecret": "",
        "encryptKey": "",
        "verificationToken": "",
        "spaceId": "",
        "reviewSpaceId": "",
        "reviewRejectFolder": "",
        "shareFolder": "",
        "rmFolder": "",
        "notify": null
    },
    "websocket": {
        "origins": [
            "https://pp-welkin-stg.nioint.com",
            "http://************:3000"
        ]
    },
    "etcd": {
        "endpoints": [
            "http://ppd-fota3-etcd-stg-01.nioint.com:2379"
        ]
    },
    "xray": {
        "address": "https://ppd-pyroscope.nioint.com"
    },
    "scnordic": {
        "url": "x",
        "apiKey": "x",
        "instanceId": ""
    },
    "alarmReceiver": {
        "cameraAcceptance": [
            {
                "type": "user_id",
                "receiverIds": [
                    "william.shen2"
                ]
            }
        ],
        "cmsAlarm": [
            {
                "type": "user_id",
                "receiverIds": [
                    "shawn.wu1"
                ]
            }
        ],
        "welkinCommonAlarm": [
            {
                "type": "chat_id",
                "receiverIds": [
                    "oc_86ca80a2620147fbd6c62103780bbb8a"
                ]
            }
        ]
    },
    "psos": {
        "max_parallel": 11
    },
    "worksheetAdapter": null,
    "extraConfig": {
        "cameraVerifyIntervalSeconds": 3600,
        "deviceModel": {
            "targetYearlyRevenue": 189000000
        },
        "evBrand": {
            "NIO": [
                "EC6",
                "EC7",
                "ES6",
                "ES7",
                "ES8",
                "ET5",
                "ET5T",
                "ET7",
                "ET9"
            ],
            "ONVO": [
                "L60"
            ]
        },
        "imageCheck": {
            "alarmThreshold": 20,
            "checkHour": 5
        }
    },
    "urlAddress": null,
    "miniApp": {
        "powerThanos": {
            "bindLimit": 0
        }
    },
    "datasightApi": null
}
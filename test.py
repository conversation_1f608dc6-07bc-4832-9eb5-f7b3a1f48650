import requests
import json
import csv
import argparse
from datetime import datetime, timedelta
import time

# if __name__ == '__main__':
#     headers = {
#         'Content-Type': 'application/json',
#     }
#     data_wlcc2 = '{ \
#       "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PowerSwap2/CC/PS-NIO-31da205c-64ba4b36/2024-08-16/13/CC-PRFF-2955-20240816133254251.jpg", \
#       "algorithm": "wlcc", \
#       "project": "PowerSwap2" \
#     }'
#     data_bscc2 = '{ \
#       "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PowerSwap2/CC/PS-NIO-31da205c-64ba4b36/2024-08-19/18/CC-BUSS-3346-20240819182412587.jpg", \
#       "algorithm": "bscc", \
#       "project": "PowerSwap2" \
#     }'
#     data_socc3_1 = '{ \
#        "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS3/CC/PS-NIO-3285ff15-7f564f27/2024-08-14/14/CC-RFS2T47-20240814142653427.jpg", \
#       "algorithm": "socc", \
#       "project": "PUS3" \
#     }'
#     data_socc3_2 = '{ \
#       "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS3/CC/PS-NIO-3285ff15-7f564f27/2024-08-09/15/CC-LFS2T47-20240809150333256.jpg", \
#      "algorithm": "socc", \
#      "project": "PUS3" \
#    }'
#     data_socc4_1 = '{ \
#       "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS4/CC/PUS-NIO-73e3fa86-e535ffe3/2024-08-19/14/CC-RFS2T47-20240819142858061.jpg", \
#       "algorithm": "socc", \
#       "project": "PUS4" \
#     }'
#     data_socc4_2 = '{ \
#           "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS4/CC/PUS-NIO-73e3fa86-e535ffe3/2024-08-19/14/CC-LFS2T47-20240819142858047.jpg", \
#           "algorithm": "socc", \
#           "project": "PUS4" \
#         }'
#     data_wlcc4 = '{ \
#       "image_url": "https://cdn-welkin-public.nio.com/welkin/image/internal/PUS4/CC/PUS-NIO-579d11b2-ea8b58bd/2024-08-20/8/CC-WLV2955-20240820080826783.jpg", \
#       "algorithm": "wlcc", \
#       "project": "PUS4" \
#     }'
#     data_picc4_1 = '{ \
#       "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS4/CC/PUS-NIO-73e3fa86-e535ffe3/2024-08-21/10/CC-PLF2345-20240821103628548.jpg", \
#       "algorithm": "picc", \
#       "project": "PUS4" \
#     }'
#     data_picc4_2 = '{ \
#       "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS4/CC/PUS-NIO-73e3fa86-e535ffe3/2024-08-21/10/CC-PLR2345-20240821103628540.jpg", \
#       "algorithm": "picc", \
#       "project": "PUS4" \
#     }'
#     data_picc4_3 = '{ \
#       "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS4/CC/PUS-NIO-73e3fa86-e535ffe3/2024-08-21/10/CC-PRF2345-20240821103628556.jpg", \
#       "algorithm": "picc", \
#       "project": "PUS4" \
#     }'
#     data_picc4_4 = '{ \
#       "image_url": "https://cdn-welkin-public-stg.nio.com/welkin/image/internal/PUS4/CC/PUS-NIO-73e3fa86-e535ffe3/2024-08-21/10/CC-PRR2345-20240821103628531.jpg", \
#       "algorithm": "picc", \
#       "project": "PUS4" \
#     }'

#     data_list = [data_bscc2, data_wlcc2, data_socc3_1, data_socc3_2, data_socc4_1, data_socc4_2, data_wlcc4, data_picc4_1, data_picc4_2, data_picc4_3, data_picc4_4]
#     for i in range(1):
#         for data in data_list:
#             response = requests.post('https://api-welkin-algorithm-stg.nioint.com/cc/v1/cc-calculation', headers=headers, data=data)
#             print(f'{data}, {response.content}')

def convert(filename, header):
    with open(f'/Users/<USER>/Downloads/{filename}.csv', newline='') as csvfile:
        reader = csv.reader(csvfile)
        csvwriterfile = open(f'{filename}_new.csv', 'w', newline='')
        writer = csv.writer(csvwriterfile)
        writer.writerow(header)
        for row in reader:
            if row[0] == 'ts':
                continue
            print(row)
            new_row = [row[0], row[2], row[3]]
            for val in row[4]:
                new_row.append(val)
            writer.writerow(new_row)
    csvwriterfile.close()

            

if __name__ == '__main__':
    # sensor_map = {}
    # header = ['ts', '电池仓步骤号', '平台侧步骤号']

    # with open('/Users/<USER>/Downloads/device2cloud.sensor-powerswap2.json') as f:
    #     data = json.load(f)
    #     for item in data:
    #         sensor_map[item['number']] = item['description']
    # # print(sensor_map)
    # i = 1
    # while i <= len(sensor_map):
    #     header.append(sensor_map[i])
    #     i += 1
    # # convert('PS-NIO-5701e4d1-1590e776bc8dc83a2d054a9107265301200010101728052453644', header)
    # convert('PS-NIO-0fcb5069-6b8ebd098dc4dac166504cc9b3be456bf1c379121728187648359', header)
    # convert('PS-NIO-d0c40807-0e1d182d5daf82ef3f8342afbb56c681de28279d1728277986379', header)

    parser = argparse.ArgumentParser()
    parser.add_argument('--day')
    args = parser.parse_args()
    day = args.day
    datetime_str = day + '01'
    partition_time = datetime.strptime(day, '%Y%m%d')
    yesterday = partition_time - timedelta(days=1)
    yesterday_timestamp = int(time.mktime(yesterday.timetuple())) * 1000
    format_str = "%Y-%m-%d %H:%M:%S.%f"
    start_time = yesterday.strftime(format_str)[:-3]
    end_time = partition_time.strftime(format_str)[:-3]
    current_time = datetime.now()
    print(f'start_time: {start_time}, end_time: {end_time}')

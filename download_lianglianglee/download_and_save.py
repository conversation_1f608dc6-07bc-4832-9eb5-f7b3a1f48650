import requests
from bs4 import BeautifulSoup
from urllib.parse import quote
from playwright.sync_api import sync_playwright
import os
import time
import random

def extract_links(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        response.encoding = 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        title = soup.title.string.strip() if soup.title else "default_title"
        safe_title = "".join(c if c.isalnum() or c in (' ', '.', '_') else '_' for c in title)
        links = [a.get('href') for a in soup.find_all('a') if a.get('href')]
        filtered_links = [link for link in links if link.startswith('/专栏')]
        full_links = [f"https://learn.lianglianglee.com{link}" for link in filtered_links]
        file_name = f'{safe_title}.txt'
        with open(file_name, "w", encoding="utf-8") as f:
            for link in full_links:
                encoded_link = quote(link, safe=':/')
                f.write(encoded_link + "\n")
        return full_links, file_name
    except Exception as e:
        print(f"Error occurred: {e}")
        return [], ""

def save_webpage_as_pdf(url, output_pdf_path):
    with sync_playwright() as p:
        browser = p.chromium.launch()
        context = browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"
        )
        page = context.new_page()
        print(f"正在打开网页: {url}")
        response = page.goto(url)
        delay_time = random.uniform(1, 3)
        print(f"随机延迟 {delay_time:.2f} 秒以模拟人类行为...")
        time.sleep(delay_time)
        final_url = page.url
        if "404" in final_url or (response and response.status == 404):
            print(f"警告: 网页导航到了错误页面，最终URL: {final_url}, 状态码: {response.status if response else '未知'}")
        title = page.title()
        print(f"网页标题: {title}")
        pdf_filename = f"{title}.pdf"
        output_pdf_path = os.path.join(os.path.dirname(output_pdf_path), pdf_filename)
        print(f"正在保存 PDF 到: {output_pdf_path}")
        page.pdf(path=output_pdf_path)
        browser.close()

def main(url):
    # 提取链接并保存到文件
    print("正在提取链接...")
    links, links_file_name = extract_links(url)
    if not links:
        print("未提取到任何链接，程序退出。")
        return

    output_dir = links_file_name.split('.txt')[0]
    os.makedirs(output_dir, exist_ok=True)

    # 读取提取的链接文件并下载 PDF
    print("开始下载 PDF...")
    with open(links_file_name, 'r', encoding='utf-8') as file:
        urls = file.readlines()
    urls = [url.strip() for url in urls if url.strip()]

    for i, target_url in enumerate(urls):
        print(f"正在处理第 {i + 1} 个 URL: {target_url}")
        output_pdf_file = os.path.join(output_dir, "webpage.pdf")
        save_webpage_as_pdf(target_url, output_pdf_file)
        if i < len(urls) - 1:
            delay_time = random.uniform(5, 15)
            print(f"完成第 {i + 1} 个 URL，随机延迟 {delay_time:.2f} 秒后继续...")
            time.sleep(delay_time)

    print("所有网页已处理完成！")
    # 删除生成的 txt 文件
    if os.path.exists(links_file_name):
        os.remove(links_file_name)
        print(f"已删除文件: {links_file_name}")
    else:
        print(f"文件不存在，无需删除: {links_file_name}")

if __name__ == "__main__":
    url = "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Kafka%E6%A0%B8%E5%BF%83%E6%8A%80%E6%9C%AF%E4%B8%8E%E5%AE%9E%E6%88%98"
    main(url)
    
    urls = [
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Kafka%E6%A0%B8%E5%BF%83%E6%BA%90%E7%A0%81%E8%A7%A3%E8%AF%BB",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Kubernetes%20%E4%BB%8E%E4%B8%8A%E6%89%8B%E5%88%B0%E5%AE%9E%E8%B7%B5",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Kubernetes%20%E5%AE%9E%E8%B7%B5%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Kubernetes%E5%85%A5%E9%97%A8%E5%AE%9E%E6%88%98%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Linux%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96%E5%AE%9E%E6%88%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/MySQL%E5%AE%9E%E6%88%9845%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/MySQL%E5%AE%9E%E6%88%98%E5%AE%9D%E5%85%B8",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/OAuth2.0%E5%AE%9E%E6%88%98%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/OKR%E7%BB%84%E7%BB%87%E6%95%8F%E6%8D%B7%E7%9B%AE%E6%A0%87%E5%92%8C%E7%BB%A9%E6%95%88%E7%AE%A1%E7%90%86-%E5%AE%8C",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/RE%E5%AE%9E%E6%88%98%E6%89%8B%E5%86%8C",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/RPC%E5%AE%9E%E6%88%98%E4%B8%8E%E6%A0%B8%E5%BF%83%E5%8E%9F%E7%90%86",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Redis%20%E6%A0%B8%E5%BF%83%E5%8E%9F%E7%90%86%E4%B8%8E%E5%AE%9E%E6%88%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Redis%20%E6%A0%B8%E5%BF%83%E6%8A%80%E6%9C%AF%E4%B8%8E%E5%AE%9E%E6%88%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Redis%20%E6%BA%90%E7%A0%81%E5%89%96%E6%9E%90%E4%B8%8E%E5%AE%9E%E6%88%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/RocketMQ%20%E5%AE%9E%E6%88%98%E4%B8%8E%E8%BF%9B%E9%98%B6%EF%BC%88%E5%AE%8C%EF%BC%89",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Serverless%20%E6%8A%80%E6%9C%AF%E5%85%AC%E5%BC%80%E8%AF%BE%EF%BC%88%E5%AE%8C%EF%BC%89",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Serverless%E8%BF%9B%E9%98%B6%E5%AE%9E%E6%88%98%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/To%20B%E5%B8%82%E5%9C%BA%E5%93%81%E7%89%8C%E5%AE%9E%E6%88%98%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Web%203.0%E5%85%A5%E5%B1%80%E6%94%BB%E7%95%A5",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/ZooKeeper%E6%BA%90%E7%A0%81%E5%88%86%E6%9E%90%E4%B8%8E%E5%AE%9E%E6%88%98-%E5%AE%8C",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/etcd%E5%AE%9E%E6%88%98%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E4%B8%AD%E9%97%B4%E4%BB%B6%E6%A0%B8%E5%BF%83%E6%8A%80%E6%9C%AF%E4%B8%8E%E5%AE%9E%E6%88%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E4%BA%92%E8%81%94%E7%BD%91%E6%B6%88%E8%B4%B9%E9%87%91%E8%9E%8D%E9%AB%98%E5%B9%B6%E5%8F%91%E9%A2%86%E5%9F%9F%E8%AE%BE%E8%AE%A1",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E4%BB%8E%200%20%E5%BC%80%E5%A7%8B%E5%AD%A6%E6%9E%B6%E6%9E%84",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E4%BB%8E0%E5%BC%80%E5%A7%8B%E5%81%9A%E5%A2%9E%E9%95%BF",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E4%BB%8E0%E5%BC%80%E5%A7%8B%E5%AD%A6%E5%A4%A7%E6%95%B0%E6%8D%AE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E4%BB%8E0%E5%BC%80%E5%A7%8B%E5%AD%A6%E5%BE%AE%E6%9C%8D%E5%8A%A1",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E4%BB%A3%E7%A0%81%E4%B9%8B%E4%B8%91",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E4%BB%A3%E7%A0%81%E7%B2%BE%E8%BF%9B%E4%B9%8B%E8%B7%AF",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%85%A8%E8%A7%A3%E7%BD%91%E7%BB%9C%E5%8D%8F%E8%AE%AE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%88%86%E5%B8%83%E5%BC%8F%E4%B8%AD%E9%97%B4%E4%BB%B6%E5%AE%9E%E8%B7%B5%E4%B9%8B%E8%B7%AF%EF%BC%88%E5%AE%8C%EF%BC%89",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%88%86%E5%B8%83%E5%BC%8F%E6%8A%80%E6%9C%AF%E5%8E%9F%E7%90%86%E4%B8%8E%E5%AE%9E%E6%88%9845%E8%AE%B2-%E5%AE%8C",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%88%86%E5%B8%83%E5%BC%8F%E6%8A%80%E6%9C%AF%E5%8E%9F%E7%90%86%E4%B8%8E%E7%AE%97%E6%B3%95%E8%A7%A3%E6%9E%90",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%88%86%E5%B8%83%E5%BC%8F%E9%87%91%E8%9E%8D%E6%9E%B6%E6%9E%84%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%88%86%E5%B8%83%E5%BC%8F%E9%93%BE%E8%B7%AF%E8%BF%BD%E8%B8%AA%E5%AE%9E%E6%88%98-%E5%AE%8C",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%8F%8D%E7%88%AC%E8%99%AB%E5%85%B5%E6%B3%95%E6%BC%94%E7%BB%8E20%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%8D%B3%E6%97%B6%E6%B6%88%E6%81%AF%E6%8A%80%E6%9C%AF%E5%89%96%E6%9E%90%E4%B8%8E%E5%AE%9E%E6%88%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF%E9%9D%A2%E8%AF%9538%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%90%8E%E7%AB%AF%E6%8A%80%E6%9C%AF%E9%9D%A2%E8%AF%9538%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%A4%A7%E5%8E%82%E5%B9%BF%E5%91%8A%E4%BA%A7%E5%93%81%E5%BF%83%E6%B3%95",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%A4%A7%E5%8E%82%E8%AE%BE%E8%AE%A1%E8%BF%9B%E9%98%B6%E5%AE%9E%E6%88%98%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%A4%A7%E8%A7%84%E6%A8%A1%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86%E5%AE%9E%E6%88%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%A6%82%E4%BD%95%E8%AE%BE%E8%AE%A1%E4%B8%80%E4%B8%AA%E7%A7%92%E6%9D%80%E7%B3%BB%E7%BB%9F",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%AE%89%E5%85%A8%E6%94%BB%E9%98%B2%E6%8A%80%E8%83%BD30%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%AE%B9%E5%99%A8%E5%AE%9E%E6%88%98%E9%AB%98%E6%89%8B%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%AE%B9%E9%87%8F%E4%BF%9D%E9%9A%9C%E6%A0%B8%E5%BF%83%E6%8A%80%E6%9C%AF%E4%B8%8E%E5%AE%9E%E6%88%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%B7%A6%E8%80%B3%E5%90%AC%E9%A3%8E",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E5%BE%AE%E6%9C%8D%E5%8A%A1%E8%B4%A8%E9%87%8F%E4%BF%9D%E9%9A%9C%2020%20%E8%AE%B2-%E5%AE%8C",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%89%93%E9%80%A0%E7%88%86%E6%AC%BE%E7%9F%AD%E8%A7%86%E9%A2%91",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%8A%80%E6%9C%AF%E4%B8%8E%E5%95%86%E4%B8%9A%E6%A1%88%E4%BE%8B%E8%A7%A3%E8%AF%BB",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%8A%80%E6%9C%AF%E7%AE%A1%E7%90%86%E5%AE%9E%E6%88%98%2036%20%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%8A%80%E6%9C%AF%E9%A2%86%E5%AF%BC%E5%8A%9B%E5%AE%9E%E6%88%98%E7%AC%94%E8%AE%B0",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%8C%81%E7%BB%AD%E4%BA%A4%E4%BB%9836%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%8E%A8%E8%8D%90%E7%B3%BB%E7%BB%9F%E4%B8%89%E5%8D%81%E5%85%AD%E5%BC%8F",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%93%8D%E4%BD%9C%E7%B3%BB%E7%BB%9F%E5%AE%9E%E6%88%9845%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%9C%B1%E8%B5%9F%E7%9A%84%E6%8A%80%E6%9C%AF%E7%AE%A1%E7%90%86%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%9D%8E%E6%99%BA%E6%85%A7%20%C2%B7%20%E9%AB%98%E5%B9%B6%E5%8F%91%E6%9E%B6%E6%9E%84%E5%AE%9E%E6%88%98%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%9E%B6%E6%9E%84%E8%AE%BE%E8%AE%A1%E9%9D%A2%E8%AF%95%E7%B2%BE%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%B6%88%E6%81%AF%E9%98%9F%E5%88%97%E9%AB%98%E6%89%8B%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%B7%B1%E5%85%A5%E6%B5%85%E5%87%BA%E4%BA%91%E8%AE%A1%E7%AE%97",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%B7%B1%E5%85%A5%E6%B5%85%E5%87%BA%E5%88%86%E5%B8%83%E5%BC%8F%E6%8A%80%E6%9C%AF%E5%8E%9F%E7%90%86",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%B7%B1%E5%85%A5%E6%B5%85%E5%87%BA%E5%8C%BA%E5%9D%97%E9%93%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%B7%B1%E5%85%A5%E6%B5%85%E5%87%BA%E5%8F%AF%E8%A7%82%E6%B5%8B%E6%80%A7",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E6%B7%B1%E5%85%A5%E6%B5%85%E5%87%BA%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%BB%84%E6%88%90%E5%8E%9F%E7%90%86",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E7%94%B1%E6%B5%85%E5%85%A5%E6%B7%B1%E5%90%83%E9%80%8F%20Docker-%E5%AE%8C",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E7%99%BD%E8%AF%9D%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8F%2028%20%E8%AE%B2%EF%BC%88%E5%AE%8C%EF%BC%89",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E7%A1%85%E8%B0%B7%E4%BA%A7%E5%93%81%E5%AE%9E%E6%88%9836%E8%AE%B2",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E7%A8%8B%E5%BA%8F%E5%91%98%E7%9A%84%E4%B8%AA%E4%BA%BA%E8%B4%A2%E5%AF%8C%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E7%A8%8B%E5%BA%8F%E5%91%98%E8%BF%9B%E9%98%B6%E6%94%BB%E7%95%A5",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E8%AE%B8%E5%BC%8F%E4%BC%9F%E7%9A%84%E6%9E%B6%E6%9E%84%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E8%AF%B4%E9%80%8F%E4%BD%8E%E4%BB%A3%E7%A0%81",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E8%AF%B4%E9%80%8F%E6%80%A7%E8%83%BD%E6%B5%8B%E8%AF%95",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E8%B6%A3%E8%B0%88%E7%BD%91%E7%BB%9C%E5%8D%8F%E8%AE%AE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E8%B7%9F%E7%9D%80%E9%AB%98%E6%89%8B%E5%AD%A6%E5%A4%8D%E7%9B%98",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B%E4%B9%8B%E7%BE%8E",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E9%80%8F%E8%A7%86HTTP%E5%8D%8F%E8%AE%AE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E9%A2%86%E5%9F%9F%E9%A9%B1%E5%8A%A8%E8%AE%BE%E8%AE%A1%E5%AE%9E%E8%B7%B5%EF%BC%88%E5%AE%8C%EF%BC%89",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E9%AB%98%E5%B9%B6%E5%8F%91%E7%B3%BB%E7%BB%9F%E5%AE%9E%E6%88%98%E8%AF%BE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E9%AB%98%E5%B9%B6%E5%8F%91%E7%B3%BB%E7%BB%9F%E8%AE%BE%E8%AE%A140%E9%97%AE",
        "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/%E9%AB%98%E6%A5%BC%E7%9A%84%E6%80%A7%E8%83%BD%E5%B7%A5%E7%A8%8B%E5%AE%9E%E6%88%98%E8%AF%BE"
    ]
    for url in urls:
        main(url)


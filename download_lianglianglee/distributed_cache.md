# 01 业务数据访问性能太低怎么办？

> 本节课主要讲缓存的基本思想、缓存的优点、缓存的代价三个部分。

## 缓存的定义

*   狭义缓存：用于加速 CPU 数据交换的 RAM。
*   广义缓存：任何可以用于数据高速交换的存储介质，可以是硬件也可以是软件。

> 缓存存在的意义就是通过开辟一个新的数据交换缓冲区，来解决原始数据获取代价太大的问题，让数据得到更快的访问。

## 缓存原理

### 缓存的基本思想

> 缓存构建的基本思想是利用**时间局限性原理**，通过**空间换时间**来达到加速数据获取的目的，同时由于缓存空间的成本较高，在实际设计架构中还要考虑**访问延迟和成本的权衡问题**。

*   **时间局限性原理**：被获取过一次的数据在未来会被多次引用。
*   **空间换时间**：开辟一块高速独立空间，提供高效访问，来达到数据获取加速的目的。
*   **性能成本 Tradeoff**：需要在系统性能和开发运行成本之间做取舍。

## 缓存的优势

*   提升访问性能
*   降低网络拥堵
*   减轻服务负载
*   增强可扩展性

> 缓存存储原始数据，可以大幅提升访问性能。缓存中存储的往往是需要频繁访问的中间数据甚至最终结果，这些数据相比 DB 中的原始数据小很多，这样就可以减少网络流量，降低网络拥堵，同时由于减少了解析和计算，调用方和存储服务的负载也可以大幅降低。

## 缓存的代价

*   增加系统的复杂度。
*   系统部署及运行的费用也会更高。
*   数据一致性问题，缓存体系本身也会存在可用性问题和分区的问题。

> 服务系统的全量原始数据存储在 DB 中，所有数据的读写都可以通过 DB 操作来获取。但 DB 读写性能低、延迟高。而 cache 的读写性能正好可以弥补 DB 的不足。
>
> 但 cache 容量小，只能存储部分访问频繁的热数据，同时，同一份数据可能同时存在 cache 和 DB，如果处理不当，就会出现数据不一致的问题。


# 02 如何根据业务来选择缓存模式和组件？

## 缓存读写模式

业务系统读写缓存有 3 种模式：

*   **Cache Aside（旁路缓存）**
*   **Read/Write Through（读写穿透）**
*   **Write Behind Caching（异步缓存写入）**

### Cache Aside

> Cache Aside 模式中，业务应用方对于写，是更新 DB 后，直接将 key 从 cache 中删除，然后由 DB 驱动缓存数据的更新；而对于读，是先读 cache，如果 cache 没有，则读 DB，同时将从 DB 中读取的数据回写到 cache。

*   特点：业务端处理所有数据访问细节，利用 ``Lazy 计算`` 的思想，确保数据以 DB 结果为准，降低 cache 和 DB 中数据不一致的概率。
*   适用场景：没有专门的存储服务，对数据一致性要求比较高，缓存数据更新比较复杂。

### Read/Write Through

> 业务应用只关注一个存储服务即可，业务方的读写 cache 和 DB 的操作，都由存储服务代理。存储服务收到业务应用的写请求时，会首先查 cache，如果数据在 cache 中不存在，则只更新 DB，如果数据在 cache 中存在，则先更新 cache，然后更新 DB。而存储服务收到读请求时，如果命中 cache 直接返回，否则先从 DB 加载，回种到 cache 后返回响应。

*   特点：存储服务封装了所有的数据处理细节，业务应用端代码只用关注业务逻辑本身，系统的隔离性更佳。写操作时，如果 cache 中没有数据则不更新，有缓存数据才更新，内存效率更高。

### Write Behind Caching

> 数据存储服务来管理 cache 和 DB 的读写。数据更新时，只更新缓存，不直接更新 DB，而是改为异步批量的方式来更新 DB。

*   特点：数据存储的写性能最高，适合一些变更特别频繁的业务，特别是可以合并写请求的业务。
*   缺点：数据的一致性变差，甚至在一些极端场景下可能会丢失数据。
*   适用场景：变更频率特别高，但对一致性要求不太高的业务。

## 缓存分类及常用缓存介绍

### 按宿主层次分类

*   **本地 Cache**：业务进程内的缓存，读写性能超高且无任何网络开销，但会随着业务系统重启而丢失。
*   **进程间 Cache**：本机独立运行的缓存，读写性能较高，不会随着业务系统重启丢数据，可以大幅减少网络开销，但运维复杂，且存在资源竞争。
*   **远程 Cache**：跨机器部署的缓存，容量大且易扩展，在互联网企业使用最广泛。不过远程缓存需要跨机访问，在高读写压力下，带宽容易成为瓶颈。

本地 Cache 的缓存组件有 `Ehcache`、`Guava Cache` 等。进程间 Cache 和远程 Cache 的缓存组件相同，有 `Memcached`、`Redis`、`Pika` 等。

### 按存储介质分类

*   **内存型缓存**：将数据存储在内存，读写性能很高，但缓存系统重启或 Crash 后，内存数据会丢失。
*   **持久化型缓存**：将数据存储到 SSD/Fusion-IO 硬盘中，容量大，数据会持久化落地，重启不丢失，但读写性能相对低。

`Memcached` 是典型的内存型缓存，而 `Pika` 以及其他基于 `RocksDB` 开发的缓存组件等则属于持久化型缓存。


# 03 设计缓存架构时需要考量哪些因素？

在设计缓存架构时，需要考虑以下因素：

## 缓存的引入及架构设计

### 缓存组件选择

*   选择合适的缓存组件，如 **Local-Cache**、**Redis**、**Memcached**、**Pika** 等。
*   根据业务需求，考虑定制开发或二次开发缓存组件。

### 缓存数据结构设计

*   根据业务访问特点，设计缓存数据结构。
*   简单 KV 读写：封装为 `String`、`Json`、`Protocol Buffer` 等格式，序列化成字节序列。
*   复杂集合数据类型：使用 `Hash`、`Set`、`List`、`Geo` 等结构，存储到支持复杂集合数据类型的缓存中，如 Redis、Pika 等。

### 缓存分布设计

*   **分布式算法选择**：
    *   **取模**：简单，每个 key 对应确定的缓存节点。
    *   **一致性 Hash**：相对复杂，key 对应的缓存节点不确定，但可以在部分节点异常时，将失效节点的数据访问均衡分散到其他正常存活的节点，保证缓存系统的稳定性。
*   **读写访问实施**：
    *   **Client 直接进行 Hash 分布定位读写**：性能最佳，但需要 Client 感知分布策略，实现复杂。
    *   **Proxy 代理进行读写路由**：Client 只需访问 Proxy，分布逻辑及部署变更都由 Proxy 处理，对业务应用开发最友好，但访问性能会有一定的损失。
*   **数据动态拆分**：
    *   当缓存数据量增长过快时，需要将数据从缓存节点进行动态拆分，水平迁移到其他缓存节点。
    *   考虑由 Proxy 进行迁移还是缓存 Server 自身进行迁移。

### 缓存架构部署及运维管理

*   **架构部署**：
    *   **分池**：核心、高并发访问的不同数据，需要分别分拆到独立的缓存池中，避免相互影响；访问量较小、非核心的业务数据，则可以混存。
    *   **分层**：对海量数据、访问超过 10～100万 级的业务数据，要考虑分层访问，并且要分摊访问量，避免缓存过载。
    *   **分 IDC**：如果业务系统需要多 IDC 部署甚至异地多活，则需要对缓存体系也进行多 IDC 部署，要考虑如何跨 IDC 对缓存数据进行更新。
    *   **异构**：某些极端场景下，还需要把多种缓存组件进行组合使用，通过缓存异构达到最佳读写性能。
*   **运维管理**：
    *   缓存服务化，更好得进行集群管理、监控运维等。

## 缓存设计架构的常见考量点

*   **读写方式**：全部整体读写，还是只部分读写及变更？是否需要内部计算？
*   **KV size**：如果单个业务的 KV size 过大，需要分拆成多个 KV 来缓存。不同缓存数据的 KV size 如果差异过大，也不能缓存在一起，避免缓存效率的低下和相互影响。
*   **Key 的数量**：如果 key 数量不大，可以在缓存中存下全量数据，把缓存当 DB 存储来用。如果数据量巨大，则在缓存中尽可能只保留频繁访问的热数据，对于冷数据直接访问 DB。
*   **读写峰值**：如果小于 10万 级别，简单分拆到独立 Cache 池即可。如果超过 10万 甚至到达 100万 级的 QPS，则需要对 Cache 进行分层处理。
*   **命中率**：对于核心高并发访问的业务，需要预留足够的容量，确保核心业务缓存维持较高的命中率。
*   **过期策略**：
    *   设置较短的过期时间，让冷 key 自动过期。
    *   让 key 带上时间戳，同时设置较长的过期时间。
*   **平均缓存穿透加载时间**：对于一些缓存穿透后，加载时间特别长或者需要复杂计算的数据，而且访问量还比较大的业务数据，要配置更多容量，维持更高的命中率，从而减少穿透到 DB 的概率，来确保整个系统的访问性能。
*   **缓存可运维性**：考虑缓存体系的集群管理，如何进行一键扩缩容，如何进行缓存组件的升级和变更，如何快速发现并定位问题，如何持续监控报警，最好有一个完善的运维平台，将各种运维工具进行集成。
*   **缓存安全性**：限制来源 IP，只允许内网访问，同时对于一些关键性指令，需要增加访问权限，避免被攻击或误操作时，导致重大后果。


# 04 缓存失效、穿透和雪崩问题怎么处理？

## 缓存失效

### 问题描述

**缓存失效** 指的是大量缓存 key 同时过期，导致大量请求直接穿透到数据库，造成数据库压力增大，请求变慢。

### 原因分析

导致缓存失效的原因通常是：

*   写入缓存时，使用了相同的过期时间，导致这批数据同时过期。

### 业务场景

*   同一批火车票、飞机票可以售卖时，系统一次性加载到缓存。
*   微博业务中，后台离线系统持续计算热门微博，并将这批热门微博批量写入缓存。
*   新 IDC 或新业务上线时，进行缓存预热，一次性加载大批热数据。

### 解决方案

使用公式：`过期时间 = base 时间 + 随机时间`。

> 相同业务数据写缓存时，在基础过期时间之上，再加一个随机的过期时间，让数据在未来一段时间内慢慢过期，避免瞬时全部过期，对 DB 造成过大压力。

## 缓存穿透

### 问题描述

**缓存穿透** 指的是查询一个不存在的 key，导致每次查询都会穿透到数据库。如果大量请求不存在的 key，会对数据库产生很大的压力。

### 原因分析

*   系统设计时，对特殊访问路径、异常访问路径考虑相对欠缺。
*   用户访问的是一个不存在的 key，查数据库返回空，不会把这个空写回缓存。

### 业务场景

*   通过不存在的 UID 访问用户。
*   通过不存在的车次 ID 查看购票信息。

### 解决方案

*   **方案一：缓存 NULL 值**

    查询这些不存在的数据时，第一次查数据库，虽然没查到结果返回 NULL，仍然记录这个 key 到缓存，只是这个 key 对应的 value 是一个特殊设置的值。

    *   改进措施：
        *   对这些不存在的 key 只存较短的时间，让它们尽快过期。
        *   将这些不存在的 key 存在一个独立的公共缓存。

*   **方案二：使用 BloomFilter 缓存过滤器**

    构建一个 **BloomFilter** 缓存过滤器，记录全量数据，这样访问数据时，可以直接通过 BloomFilter 判断这个 key 是否存在，如果不存在直接返回即可，根本无需查缓存和数据库。

### BloomFilter

**BloomFilter** 是一个非常有意思的数据结构，不仅仅可以挡住非法 key 攻击，还可以低成本、高性能地对海量数据进行判断。

> BloomFilter 的目的是检测一个元素是否存在于一个集合内。它的原理，是用 bit 数据组来表示一个集合，对一个 key 进行多次不同的 Hash 检测，如果所有 Hash 对应的 bit 位都是 1，则表明 key 非常大概率存在，平均单记录占用 1.2 字节即可达到 99%，只要有一次 Hash 对应的 bit 位是 0，就说明这个 key 肯定不存在于这个集合内。

BloomFilter 的算法：

1.  分配一块内存空间做 bit 数组，数组的 bit 位初始值全部设为 0。
2.  加入元素时，采用 k 个相互独立的 Hash 函数计算，然后将元素 Hash 映射的 K 个位置全部设置为 1。
3.  检测 key 时，仍然用这 k 个 Hash 函数计算出 k 个位置，如果位置全部为 1，则表明 key 存在，否则不存在。

BloomFilter 的优势：

*   全内存操作，性能很高。
*   空间效率非常高，要达到 1% 的误判率，平均单条记录占用 1.2 字节即可。

## 缓存雪崩

### 问题描述

**缓存雪崩** 指的是部分缓存节点不可用，导致整个缓存体系甚至服务系统不可用的情况。

缓存雪崩按照缓存是否 rehash（即是否漂移）分两种情况：

*   缓存不支持 rehash 导致的系统雪崩不可用。
*   缓存支持 rehash 导致的缓存雪崩不可用。

### 原因分析

*   缓存不进行 rehash 时产生的雪崩：较多缓存节点不可用，请求穿透导致数据库也过载不可用，最终整个系统雪崩不可用的。
*   缓存支持 rehash 时产生的雪崩：流量洪峰到达，引发部分缓存节点过载 Crash，然后因 rehash 扩散到其他缓存节点，最终整个缓存体系异常。

### 业务场景

*   微博最初很多业务缓存采用一致性 Hash+rehash 策略，在突发洪水流量来临时，部分缓存节点过载 Crash 甚至宕机，然后这些异常节点的请求转到其他缓存节点，又导致其他缓存节点过载异常，最终整个缓存池过载。
*   机架断电，导致业务缓存多个节点宕机，大量请求直接打到数据库，也导致数据库过载而阻塞，整个系统异常。

### 解决方案

*   **方案一：对业务数据库的访问增加读写开关**

    当发现数据库请求变慢、阻塞，慢请求超过阀值时，就会关闭读开关，部分或所有读数据库的请求进行 failfast 立即返回，待数据库恢复后再打开读开关。

*   **方案二：对缓存增加多个副本**

    缓存异常或请求 miss 后，再读取其他缓存副本，而且多个缓存副本尽量部署在不同机架，从而确保在任何情况下，缓存系统都会正常对外提供服务。

*   **方案三：对缓存体系进行实时监控**

    当请求访问的慢速比超过阀值时，及时报警，通过机器替换、服务替换进行及时恢复；也可以通过各种自动故障转移策略，自动关闭异常接口、停止边缘服务、停止部分非核心功能措施，确保在极端场景下，核心功能的正常运行。


# 05 缓存数据不一致和并发竞争怎么处理？

## 数据不一致

### 问题描述
同一份数据可能同时存在于 **DB** 和 **缓存** 中，导致 **DB** 和 **缓存** 的数据不一致。 多个缓存副本之间也可能存在不一致现象。

### 原因分析
不一致问题通常与缓存更新异常有关，例如：
*   更新 DB 后，写缓存失败，导致缓存中是老数据。
*   采用一致性 Hash 分布，同时采用 rehash 自动漂移策略，在节点多次上下线之后，产生脏数据。
*   缓存有多个副本时，更新某个副本失败，导致该副本的数据是老数据。

### 业务场景
*   缓存机器的带宽被打满，或者机房网络出现波动时，缓存更新失败。
*   缓存 rehash 时，某个缓存机器反复异常，多次上下线，更新请求多次 rehash，导致一些缓存节点产生脏数据。

### 解决方案
尽量保证数据的一致性，可以根据实际情况选择以下方案：

1.  **重试机制:** cache 更新失败后，进行重试。如果重试失败，则将失败的 key 写入队列机服务，待缓存访问恢复后，将这些 key 从缓存删除。这些 key 在再次被查询时，重新从 DB 加载，从而保证数据的一致性。
2.  **缩短缓存时间:** 缓存时间适当调短，让缓存数据及早过期后，然后从 DB 重新加载，确保数据的最终一致性。
3.  **避免 rehash 漂移:** 不采用 rehash 漂移策略，而采用缓存分层策略，尽量避免脏数据产生。

## 数据并发竞争

### 问题描述
在高并发访问场景，缓存访问没有找到数据，大量请求并发查询 DB，导致 DB 压力大增。

### 原因分析
多个进程/线程中，大量并发请求获取相同的数据，而该数据 key 因为过期、被剔除等原因在缓存中不存在，这些进程/线程之间没有任何协调，一起并发查询 DB，导致 DB 压力大增。

### 业务场景
*   车票系统：某个火车车次缓存信息过期，但仍然有大量用户在查询该车次信息。
*   微博系统：某条微博正好被缓存淘汰，但这条微博仍然有大量的转发、评论、赞。

### 解决方案
要解决并发竞争，有以下 2 种方案：

1.  **全局锁:** 缓存请求 miss 后，先尝试加全局锁，只有加全局锁成功的线程，才可以到 DB 去加载数据。其他进程/线程在读取缓存数据 miss 时，如果发现这个 key 有全局锁，就进行等待，待之前的线程将数据从 DB 回种到缓存后，再从缓存获取。
2.  **多备份:** 对缓存数据保持多个备份，即便其中一个备份中的数据过期或被剔除了，还可以访问其他备份，从而减少数据并发竞争的情况。


# 06 Hot Key和Big Key引发的问题怎么应对？

## Hot Key

### 问题描述

当突发事件发生时，大量用户同时访问**热点信息**，导致缓存节点过载甚至崩溃。

### 原因分析

超大量的请求访问热点事件对应的 `key`，流量集中打在一个缓存节点机器，导致缓存机器达到物理极限，访问变慢、卡顿。

### 业务场景

*   明星结婚、离婚、出轨等突发事件
*   奥运、春节等重大活动或节日
*   秒杀、双12、618 等线上促销活动

### 解决方案

1.  **找出 Hot Key：**
    *   提前评估（重要节假日、线上促销活动等）
    *   `Spark` 实时分析（突发事件）
    *   `Hadoop` 离线计算（之前已发出的事情，逐步发酵成为热 key ）
2.  **分散处理：**
    *   将热 key 分散为 `hotkey#1`、`hotkey#2`、`hotkey#3` 等，分散到多个缓存节点。
    *   Client 端随机访问其中某个后缀的 hotkey，打散请求。
3.  **多副本+多级缓存架构**
4.  **实时监控与快速扩容：**
    *   通过监控体系对缓存的 `SLA` 实时监控。
    *   快速扩容减少热 key 冲击。
5.  **本地缓存：**
    *   业务端使用本地缓存记录热 key，减少对远程缓存的冲击。

## Big Key

### 问题描述

缓存访问时，部分 `Key` 的 `Value` 过大，导致读写、加载易超时。

### 原因分析

*   `Big Key` 占总体数据比例小，易被频繁剔除，DB 反复加载。
*   大量访问 `Big Key`，缓存组件网卡、带宽被打满。
*   `Big Key` 缓存字段多，字段变更频繁，读写相互影响。
*   `Big Key` 被淘汰后，DB 加载耗时大。

### 业务场景

*   保存用户最新 1 万个粉丝
*   用户个人信息缓存（基本资料、关系图谱计数、feed 统计等）
*   长微博内容

### 解决方案

1.  **Memcached 缓存：**
    *   设置缓存阀值，Value 长度超过阀值则启用压缩。
    *   预写足够数据的大 key，让 Memcached 预先分配足够多的 trunk size 较大的 slab。
2.  **Redis 缓存：**
    *   扩展新的数据结构。
    *   Client 在大 key 写缓存之前进行序列化构建，然后通过 restore 一次性写入。
3.  **Key 拆分：**
    *   将大 key 分拆为多个 key，减少大 key 存在。
    *   设置较长的过期时间，尽量不淘汰大 key。


# 07 MC为何是应用最广泛的缓存组件？

## Memcached 原理及特性

### 原理

> **Memcached (MC)** 是一个开源的、高性能的分布式 **key/value** 内存缓存系统。它是一个键值类型的 **NoSQL** 组件。

*   **NoSQL**：Not SQL，泛指非关系型数据存储。通过聚合模型进行数据处理。
*   聚合模型主要分为：**key/value 键值对**、列族、图形等几种方式。
*   MC、Redis 等都是 **key/value** 类型的 NoSQL 存储组件。
*   MC 是一个典型的 ``内存型缓存组件``，重启会丢失所有数据。
*   MC 组件之间相互不通信，完全由 client 对 key 进行 Hash 后分布和协同。
*   MC 采用多线程处理请求，由一个主线程和任意多个工作线程协作，从而充分利用多核，提升 IO 效率。

### slab 机制

> MC 内部采用 **slab 机制** 来管理内存分配。

*   内存划分为一系列相同大小的 **slab 空间**，每个 slab 只管理一定范围内的数据存储。
*   Mc 内的内存分配以 slab 为单位，默认情况下一个 slab 是 1MB，可以通过 -I 参数在启动时指定其他数值。
*   slab 空间内部，会被进一步划分为一系列固定大小的 **chunk**。
*   每个 chunk 内部存储一个 **Item**，利用 Item 结构存储数据。
*   Item存储完 key/value 数据后，一般还会有多余的空间，这个多余的空间就被浪费了。
*   为了提升内存的使用效率，chunk size 就不能太大，而要尽量选择与 key/value size 接近的 ，从而减少 chunk 内浪费的空间。
*   一组具有相同 chunk size 的所有 slab，就组成一个 **slabclass**。
*   不同 slabclass 的 chunk size 按递增因子一次增加。
*   Mc 通过 slabclass 来管理一组 slab 内的存储空间的。
*   每个 slabclass 内部有一个 **freelist** ，包含这组 slab 里所有空闲的 chunk，当需要存储数据时，从这个 freelist 里面快速分配一个 chunk 做存储空间。
*   当 Item 数据淘汰剔除时，这个 Item 所在的 chunk 又被回收至这个 freelist。
*   实际 key/value 是存在 Item 结构中，所以对 key/value 的存储空间分配就转换为对 Item 的分配。
*   Item 空间的分配有 2 种方式：
    *   Mc 有空闲空间，则从 slabclass 的 freelist 分配；
    *   如果没有空闲空间，则从对应 slabclass id 对应的 LRU 中剔除一个 Item，来复用这个 Item 的空间。
*   Mc 是通过 **哈希表 Hashtable** 来定位 key 的。
*   Hashtable 可以看作是一个内存空间连续的大数组，而这个大数据的每一个槽位对应一个 key 的 Hash 值，这个槽位也称 bucket。
*   由于不同 key 的 Hash 值可能相同，所以 Mc 在 Hashtable 的每个捅内部再用一个单向链表，来解决 Hash 冲突的问题。
*   Mc 内部是通过 **LRU** 来管理存储 Item 数据的，当内存不足时，会从 LRU 队尾中剔除一个过期或最不活跃的 key，供新的 Item 使用。

### 特性

*   Mc 最大的特性是 ``高性能``，单节点压测性能能达到百万级的 QPS。
*   Mc 的访问协议很简单，只有 get/set/cas/touch/gat/stats 等有限的几个命令。
*   Mc 存储结构很简单，只存储简单的 key/value 键值对，而且对 value 直接以二进制方式存储，不识别内部存储结构，所以有限几个指令就可以满足操作需要。
*   Mc 完全基于内存操作，在系统运行期间，在有新 key 写进来时，如果没有空闲内存分配，就会对最不活跃的 key 进行 eviction 剔除操作。
*   Mc 服务节点运行也特别简单，不同 Mc 节点之间互不通信，由 client 自行负责管理数据分布。


# 08 MC系统架构是如何布局的？

## 系统架构

MC 的系统架构主要包括以下 5 个部分：

*   **网络处理模块**：基于 Libevent 实现，通过多路复用 IO（如 epoll）进行网络 IO 接入和读写处理，支持高并发和高 IO 吞吐效率。
*   **多线程处理模块**：包括主线程、工作线程和辅助线程（如 Item 爬虫线程、LRU 维护线程、哈希表维护线程等），充分利用机器的多个核心，提高网络 IO 性能和数据处理能力。
*   **哈希表（Hashtable）**：用于快速定位 key。数据 Item 结构在存入 slab 的 chunk 后，也会被存放到 Hashtable 中。通过单向链表解决 Hash 冲突。
*   **LRU 机制**：用于冷数据淘汰。Mc 默认启用分段 LRU，每个 slabclass id 对应 TEMP、HOT、WARM 和 COLD 四个 LRU。
    *   TEMP LRU：存放剩余过期时间很短的 Item（默认 61 秒以内），避免锁竞争，性能更高。
    *   HOT LRU：内部不搬运，满时 Active 状态 Item 迁移到 WARM 队列，否则迁移到 COLD 队列。
    *   WARM LRU：Item 再次访问搬到队首，否则迁移到 COLD 队列。
    *   COLD LRU：存放最不活跃的 Item，内存满时队尾 Item 被剔除。
*   **slab 内存分配模块**：通过 slab 机制分配管理内存，解决内存碎片问题。

### 哈希表扩容

当表中 Item 数量大于哈希表 bucket 节点数的 1.5 倍时，进行扩容。

扩容时，Mc 内部使用两张 Hashtable：

*   `primary_hashtable`：主哈希表
*   `old_hashtable`：旧哈希表

扩容过程中，维护线程会将旧表的 Item 指针逐步复制插入到新主哈希表。迁移过程中，用户请求会同时查旧表和新的主表，当数据全部迁移完成，所有的操作就重新回到主表中进行。

### slab 分配机制

Mc 启动时，会创建 64 个 slabclass，但索引为 0 的 slabclass 做 slab 重新分配之用，基本不参与其他 slabclass 的日常分配活动。每个 slabclass 会根据需要不断分配默认大小为 1MB 的 slab。

每个 slab 又被分为相同大小的 chunk。chunk 就是 Mc 存储数据的基本存储单位。slabclass 1 的 chunk size 最小，默认最小 chunk 的大小是 102 字节，后续的 slabclass 会按照增长因子逐步增大 chunk size，具体数值会进一步对 8 取整。Mc 默认的增长因子是 1.25，启动时可以通过 -f 将增长因子设为其他值。

Mc slab 中的 chunk 中通过 Item 结构存 key/value 键值对，Item 结构体的头部存链表的指针、flag、过期时间等，然后存 key 及 value。

每次新分配一个 slab 后，会将 slab 空间等分成相同 size 的 chunk，这些 chunk 会被加入到 slabclass 的 freelist 中，在需要时进行分配。分配出去的 chunk 存储 Item 数据，在过期被剔除后，会再次进入 freelist，供后续使用。


# 09 MC是如何使用多线程和状态机来处理请求命令的？

## 网络模型

MC 基于 **Libevent** 实现**多线程网络 IO 模型**。IO 处理线程分为**主线程**和**工作线程**。

*   主线程：负责监听及建立连接。
*   工作线程：负责对建立的连接进行网络 IO 读取、命令解析、处理及响应。

### 主线程

主线程在监听端口时，当有连接到来，主线程 `accept` 该连接，并将连接调度给工作线程。

> 调度处理逻辑：主线程先将 fd 封装成一个 `CQ_ITEM` 结构，并存入**新连接队列**中，然后轮询一个工作线程，并通过**管道**向该工作线程发送通知。

工作线程监听到通知后，会从新连接队列获取一个连接，然后开始从这个连接读取网络 IO 并处理。主线程的这个处理逻辑主要在**状态机**中执行，对应的连接状态为 `conn_listening`。

### 工作线程

工作线程监听到主线程的管道通知后，会从连接队列弹出一个新连接，然后就会创建一个 `conn` 结构体，注册该 `conn` 读事件，然后继续监听该连接上的 IO 事件。

> 后续这个连接有命令进来时，工作线程会读取 client 发来的命令，进行解析并处理，最后返回响应。工作线程的主要处理逻辑也是在**状态机**中，一个名叫 `drive_machine` 的函数。

## 状态机

状态机由主线程和工作线程共享，实际是采用 `switch-case` 来实现的。

### 主线程状态机

主线程在状态机中只处理 `conn_listening` 状态，负责 `accept` 新连接和调度新连接给工作线程。状态机中其他状态处理基本都在工作线程中进行。

### 工作线程状态机

工作线程的状态机处理逻辑包括：

1.  刚建立 `conn` 连接结构体时进行的一些重置操作。
2.  然后注册读事件，在有数据进来时，读取网络数据，并进行解析并处理。
3.  如果是读取指令或统计指令，至此就基本处理完毕，接下来将响应写入连接缓冲。
4.  如果是更新指令，在进行初步处理后，还会继续读取 value 部分，再进行存储或变更，待变更完毕后将响应写入连接缓冲。
5.  最后再将响应写给 client。

> 响应 client 后，连接会再次重置连接状态，等待进入下一次的命令处理循环中。这个过程主要包含了 `conn_new_cmd`、`conn_waiting`、`conn_read`、`conn_parse_cmd`、`conn_nread`、`conn_write`、`conn_mwrite`、`conn_closing` 这 8 个状态事件。

#### 工作线程状态事件及逻辑处理

*   **conn_new_cmd**

    *   worker 线程创建 `conn` 对象，这个连接初始状态就是 `conn_new_cmd`。
    *   连接命令处理完毕，准备接受新指令时，也会将连接的状态设置为 `conn_new_cmd` 状态。
    *   进入 `conn_new_cmd` 后，工作线程会调用 `reset_cmd_handler` 函数，重置 `conn` 的 `cmd` 和 `substate` 字段，并在必要时对连接 `buf` 进行收缩。
*   **conn_parse_cmd**

    *   工作线程处理完 `conn_new_cmd` 状态的主要逻辑后，如果读缓冲区有数据可以读取，则进入 `conn_parse_cmd` 状态，否则就会进入到 `conn_waiting` 状态，等待网络数据进来。
*   **conn_waiting**

    *   连接进入 `conn_waiting` 状态后，处理逻辑很简单，直接通过 `update_event` 函数注册读事件即可，之后会将连接状态更新为 `conn_read`。
*   **conn_read**

    *   当工作线程监听到网络数据进来，连接就进入 `conn_read` 状态。
    *   对 `conn_read` 的处理，是通过 `try_read_network` 从 socket 中读取网络数据。
        *   如果读取失败，则进入 `conn_closing` 状态，关闭连接。
        *   如果没有读取到任何数据，则会返回 `conn_waiting`，继续等待 client 端的数据到来。
        *   如果读取数据成功，则会将读取的数据存入 `conn` 的 `rbuf` 缓冲，并进入 `conn_parse_cmd` 状态，准备解析 cmd。
*   **conn_parse_cmd (解析命令)**

    *   工作线程首先通过 `try_read_command` 读取连接的读缓冲，并通过 `\n` 来分隔数据报文的命令。
    *   如果命令首行长度大于 1024，关闭连接，这就意味着 key 长度加上其他各项命令字段的总长度要小于 1024字节。
    *   接下来会在 `process_command` 函数中对首行指令进行处理。
    *   `process_command` 用来处理 Mc 的所有协议指令，会首先按照空格分拆报文，确定命令协议类型，分派给 `process_XX_command` 函数处理。
    *   Mc 的命令协议可以细分为 `get` 类型、`update` 类型、`delete` 类型、`算术`类型、`touch` 类型、`stats` 类型，以及其他类型。
    *   只有读取到 `\n`，有了完整的命令首行协议，才会进入 `process_command`，否则会跳转到 `conn_waiting`，继续等待客户端的命令数据报文。
    *   在 `process_command` 处理中，如果是获取类命令，在获取到 key 对应的 value 后，则跳转到 `conn_mwrite`，准备写响应给连接缓冲。
    *   而对于 `update` 变更类型的指令，则需要继续读取 value 数据，此时连接会跳转到 `conn_nread` 状态。
    *   在 `conn_parse_cmd` 处理过程中，如果遇到任何失败，都会跳转到 `conn_closing` 关闭连接。
*   **complete_nread**

    *   对于 `update` 类型的协议指令，从 `conn` 继续读取 value 数据。
    *   读取到 value 数据后，会调用 `complete_nread`，进行数据存储处理；数据处理完毕后，向 `conn` 的 `wbuf` 写响应结果。
    *   然后 `update` 类型处理的连接进入到 `conn_write` 状态。
*   **conn_write**

    *   连接 `conn_write` 状态处理逻辑很简单，直接进入 `conn_mwrite` 状态。
    *   或者当 `conn` 的 `iovused` 为 0 或对于 `udp` 协议，将响应写入 `conn` 消息缓冲后，再进入 `conn_mwrite` 状态。
*   **conn_mwrite**

    *   进入 `conn_mwrite` 状态后，工作线程将通过 `transmit` 来向客户端写数据。
    *   如果写数据失败，跳转到 `conn_closing`，关闭连接退出状态机。
    *   如果写数据成功，则跳转到 `conn_new_cmd`，准备下一次新指令的获取。
*   **conn_closing**

    *   在任何状态的处理过程中，如果出现异常，就会进入到这个状态，关闭连接。

## Mc 命令处理全流程

1.  Mc 启动后，主线程监听并准备接受新连接接入。当有新连接接入时，主线程进入 `conn_listening` 状态，accept 新连接，并将新连接调度给工作线程。
2.  Worker 线程监听管道，当收到主线程通过管道发送的消息后，工作线程中的连接进入 `conn_new_cmd` 状态，创建 `conn` 结构体，并做一些初始化重置操作，然后进入 `conn_waiting` 状态，注册读事件，并等待网络 IO。
3.  有数据到来时，连接进入 `conn_read` 状态，读取网络数据。
4.  读取成功后，就进入 `conn_parse_cmd` 状态，然后根据 Mc 协议解析指令。
5.  对于读取指令，获取到 value 结果后，进入 `conn_mwrite` 状态。
6.  对于变更指令，则进入 `conn_nread`，进行 value 的读取，读取到 value 后，对 key 进行变更，当变更完毕后，进入 `conn_write`，然后将结果写入缓冲。然后和读取指令一样，也进入 `conn_mwrite` 状态。
7.  进入到 `conn_mwrite` 状态后，将结果响应发送给 client。发送响应完毕后，再次进入到 `conn_new_cmd` 状态，进行连接重置，准备下一次命令处理循环。
8.  在读取、解析、处理、响应过程，遇到任何异常就进入 `conn_closing`，关闭连接。


# 10 MC是怎么定位key的

本节课深入学习 Memcached 如何进行 key 定位，如何淘汰回收过期失效 key，分析 Mc 的内存管理 slab 机制，以及 Mc 进行数据存储维护的关键机理，最后还会对 Mc 进行完整的协议分析。

## key 定位

### 哈希表

Mc 将数据存储在 Item 中，Item 由 slabclass 的 4 个 LRU 管理。LRU 通过双向链表实现，增删改高效，但定位 key 性能低下，只能链表遍历。因此，Mc 通过 **Hashtable (哈希表)** 记录管理 Item，通过对 key 进行哈希计算，快速定位和读取 key/value 所在的 Item。

> 哈希表也称散列表，可以通过把 key 映射到哈希表中的一个位置来快速访问记录，定位 key 的时间复杂度只有 O(1)。

Mc 的哈希表实际是一个一维指针数组，数组的每个位置称作一个 **bucket (桶)**。Mc 启动时，默认构建一个拥有 6.4万 个桶的哈希表，随着新 key 的不断插入，哈希表中的元素超过阀值后，会对哈希表进行扩容，最大可以构建 2 的 32 次方个桶的哈希表，最多只能有不超过 43亿 个桶。

### 哈希表设计

哈希表设计的 2 个关键点：

1.  **哈希算法**
2.  **哈希冲突解决方案**

Mc 使用的哈希算法有 2 种：**Murmur3 Hash** 和 **Jenkins Hash**。Mc 当前版本，默认使用 Murmur3 Hash 算法。

不同的 key 通过 Hash 计算，被定位到了相同的桶，这就是 **哈希冲突**。Mc 是通过对每个桶启用一个 **单向链表**，来解决哈希冲突问题的。

### 定位 key

Memcached 定位 key 时，首先根据 key 采用 Murmur3 或者 Jenkins 算法进行哈希计算，得到一个 32 位的无符号整型输出，存储到变量 hv 中。因为哈希表一般没有 2^32 那么大，所以需要将 key 的哈希值映射到哈希表的范围内。

Mc 采用 **取模算法** 作为映射函数，即采用 `hv%hashsize` 进行计算。由于普通的取模运算比较耗时，所以 Mc 将哈希表的长度设置为 2 的 n 次方，采用 **位运算** 进行优化，即采用 `hv&hashmask` 来计算。`hashmask` 即 2 的 n 次方 减 1。

定位到 key 所在的桶的位置后，如果是插入一个新数据，则将数据 Item 采用 **头部插入法** 插入桶的单向链表中。如果是查找，则轮询对应哈希桶中的那个单向链表，依次比对 key 字符串，key 相同则找到数据 Item。

如果哈希表桶中元素太多，这个链表轮询耗时会比较长，所以在哈希表中元素达到桶数的 1.5 倍之后，Mc 会对哈希表进行 2 倍扩容。由于哈希表最多只有 43 亿左右个桶，所以性能考虑，单个 Mc 节点最多存储 65亿 个 key/value。如果要存更多 key，则需要修改 Mc 源码，将最大哈希，即 `HASHPOWER_MAX`， 进行调大设置。

### 哈希表扩容

当 Mc 的哈希表中，Item 数量大于 1.5 倍的哈希桶数量后，Mc 就对哈希表进行扩容处理。Mc 的哈希扩容是通过哈希维护线程进行处理的。

准备开始扩容时，哈希维护线程会首先将所有 IO 工作线程和辅助线程进行暂停，其中辅助线程包括 LRU 维护线程、slab 维护线程、LRU 爬虫线程。待这些线程暂停后，哈希维护线程会将当前的主哈希表设为旧哈希表，然后将新的主哈希表扩容之前的 2 倍容量。然后，工作线程及辅助线程继续工作，同时哈希维护线程开始逐步将 Item 元素从旧哈希表迁移到主哈希表。

Mc 在启动时，会根据设置的工作线程数，来构建 一个 Item 锁哈希表，线程越多，构建的锁哈希表越大，对于 4 个线程，锁哈希表有 4096 个桶，对于 10 个线程，锁哈希表会有 8192 个桶，Item 锁哈希表最多有 32k 个桶，1k 是 1024，即最多即 32768 个桶。Mc 的锁哈希表中，每个桶对应一个 Item 锁，所以 Mc 最多只有 32768 个 Item 锁。

Mc 哈希表在读取、变更以及扩容迁移过程中，先将 key hash 定位到 Item 锁哈希表的锁桶，然后对 Item 锁进行加锁，然后再进行实际操作。实际上，除了在哈希表，在其他任何时候，只要涉及到在对 Item 的操作，都会根据 Item 中的 key，进行 Item 哈希锁桶加锁，以避免 Item 被同时读写而产生脏数据。Mc 默认有 4096 个锁桶，所以对 key 加锁时，冲突的概率较小，而且 Mc 全部是内存操作，操作速度很快，即便申请时锁被占用，也会很快被释放。

Mc 哈希表在扩容时，哈希表维护线程，每次按 桶链表纬度 迁移，即一次迁移一个桶里单向链表的所有 Item 元素。在扩容过程中，如果要查找或插入 key，会参照迁移位置选择哈希表。如果 key 对应的哈希桶在迁移位置之前，则到新的主哈希表进行查询或插入，否则到旧哈希表进行查询和插入。待全部扩容迁移完毕，所有的处理就会全部在新的主哈希表进行。
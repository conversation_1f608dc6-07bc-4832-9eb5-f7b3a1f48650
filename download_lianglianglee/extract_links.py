import requests
from bs4 import BeautifulSoup
from urllib.parse import quote  # 引入 quote 进行 URL 编码

def extract_links(url):
    try:
        # 发送 HTTP 请求获取页面内容
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功
        response.encoding = 'utf-8'  # 设置编码

        # 使用 BeautifulSoup 解析 HTML
        soup = BeautifulSoup(response.text, 'html.parser')

        # 提取网页标题并清理非法字符
        title = soup.title.string.strip() if soup.title else "default_title"
        safe_title = "".join(c if c.isalnum() or c in (' ', '.', '_') else '_' for c in title)

        # 查找所有 <a> 标签并提取 href 属性
        links = [a.get('href') for a in soup.find_all('a') if a.get('href')]

        # 筛选以 /专栏 开头的链接
        filtered_links = [link for link in links if link.startswith('/专栏')]

        # 拼接完整 URL
        full_links = [f"https://learn.lianglianglee.com{link}" for link in filtered_links]

        # 对链接进行 URL 编码后保存到文件
        file_name = f"{safe_title}.txt"  # 动态生成文件名
        with open(file_name, "w", encoding="utf-8") as f:
            for link in full_links:
                encoded_link = quote(link, safe=':/')  # 对链接进行 URL 编码，保留 : 和 / 不被编码
                f.write(encoded_link + "\n")

        return full_links
    except Exception as e:
        print(f"Error occurred: {e}")
        return []

if __name__ == "__main__":
    url = "https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/24%E8%AE%B2%E5%90%83%E9%80%8F%E5%88%86%E5%B8%83%E5%BC%8F%E6%95%B0%E6%8D%AE%E5%BA%93-%E5%AE%8C"
    links = extract_links(url)
    # print("页面中的所有链接：")
    # for link in links:
    #     print(link)
    
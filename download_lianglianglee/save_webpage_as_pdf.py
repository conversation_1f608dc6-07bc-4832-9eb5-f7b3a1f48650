import argparse  # 新增：用于解析命令行参数
from playwright.sync_api import sync_playwright
import os
import time  # 新增：用于随机延迟
import random  # 新增：用于生成随机延迟时间

def save_webpage_as_pdf(url, output_pdf_path):
    """
    使用 Playwright 打开指定 URL 并将网页内容保存为 PDF。

    :param url: 要打开的网页 URL
    :param output_pdf_path: 保存 PDF 的文件路径
    """
    with sync_playwright() as p:
        # 启动浏览器（这里使用 Chromium）
        browser = p.chromium.launch()
        context = browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36"  # 新增：设置 User-Agent
        )
        page = context.new_page()
        
        # 打开指定 URL
        print(f"正在打开网页: {url}")
        response = page.goto(url)

        # 新增：随机延迟，模拟人类行为
        delay_time = random.uniform(1, 3)  # 随机生成 1 到 3 秒的延迟
        print(f"随机延迟 {delay_time:.2f} 秒以模拟人类行为...")
        time.sleep(delay_time)

        # 检查页面是否导航到404或其他错误页面
        final_url = page.url
        if "404" in final_url or (response and response.status == 404):
            print(f"警告: 网页导航到了错误页面，最终URL: {final_url}, 状态码: {response.status if response else '未知'}")

        # 获取网页标题
        title = page.title()
        print(f"网页标题: {title}")
        
        # 将网页保存为 PDF，文件名基于网页标题
        pdf_filename = f"{title}.pdf"
        output_pdf_path = os.path.join(os.path.dirname(output_pdf_path), pdf_filename)
        print(f"正在保存 PDF 到: {output_pdf_path}")
        page.pdf(path=output_pdf_path)
        
        # 关闭浏览器
        browser.close()

if __name__ == "__main__":
    # 新增：解析命令行参数
    parser = argparse.ArgumentParser(description="将网页保存为 PDF 并存放到指定目录。")
    parser.add_argument("output_dir", type=str, help="存放下载文件的目录名（相对于当前路径）")
    args = parser.parse_args()

    # 新增：获取用户指定的目录并确保其存在
    output_dir = os.path.join(os.path.dirname(__file__), args.output_dir)
    os.makedirs(output_dir, exist_ok=True)

    # 读取 extracted_links.txt 文件中的 URL 列表
    links_file_path = os.path.join(os.path.dirname(__file__), f"{args.output_dir}.txt")
    with open(links_file_path, 'r', encoding='utf-8') as file:
        urls = file.readlines()
    
    # 去除每行的换行符和空格
    urls = [url.strip() for url in urls if url.strip()]
    
    # 遍历每个 URL 并保存为 PDF
    for i, target_url in enumerate(urls):
        print(f"正在处理第 {i + 1} 个 URL: {target_url}")
        
        # 动态生成 PDF 文件路径，保存到用户指定的目录
        output_pdf_file = os.path.join(output_dir, "webpage.pdf")  # 默认占位文件名，后续会被覆盖
        save_webpage_as_pdf(target_url, output_pdf_file)
        
        # 如果不是最后一个 URL，则随机延迟一段时间
        if i < len(urls) - 1:
            delay_time = random.uniform(5, 15)  # 随机生成 5 到 15 秒的延迟
            print(f"完成第 {i + 1} 个 URL，随机延迟 {delay_time:.2f} 秒后继续...")
            time.sleep(delay_time)

    print("所有网页已处理完成！")

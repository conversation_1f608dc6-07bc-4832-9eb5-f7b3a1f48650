FROM artifactory.nioint.com/dd-venus-docker-virtual/base/rockylinux/rockylinux8.7python3.8:1.0

LABEL name="rockylinux8.7python3.8cc" version="1.0"

ENV PYTHONDONTWRITEBYTECODE=1

RUN dnf install mesa-libGL -y

RUN ln -s /usr/bin/python3 /usr/bin/python

RUN python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple --no-cache-dir \
    Flask==3.0.3 Jinja2==3.1.4 requests==2.31.0 Werkzeug==3.0.3 python-json-logger==2.0.7 \
    opencv-python==******** numpy~=1.24.2 torch==2.0.1 torchvision==0.15.2 gunicorn==23.0.0 \
    openmim==0.3.9 mmcv==2.0.0 mmengine==0.7.1 terminaltables==3.1.10 pycocotools==2.0.7 \
    shapely==2.0.6 scipy==1.10.1
